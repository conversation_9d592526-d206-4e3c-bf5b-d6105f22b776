import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute } from '@angular/router';

import { SharedModule } from '@elements/ui';
import { InventoryService } from '@elevar-clients/api';

@Component({
  selector: 'lib-stock-items-details',
  imports: [CommonModule, SharedModule],
  templateUrl: './stock-items-details.component.html',
  styleUrl: './stock-items-details.component.scss'
})
export class StockItemsDetailsComponent {
  private readonly _route = inject(ActivatedRoute);
  private readonly _inventoryService = inject(InventoryService);

  stockName = this._route.snapshot.params['id'];
  stock$ = this._inventoryService.getStockReconciliationById(this.stockName);
  
  // Accordion states
  accordionState = {
    header: true,
    items: true,
    payments: true,
    summary: true
  };

  /**
   * Toggle the specified accordion section
   * @param section The accordion section to toggle
   */
  toggleAccordion(section: 'header' | 'items' | 'payments' | 'summary'): void {
    this.accordionState[section] = !this.accordionState[section];
  }

  /**
   * Calculate the total amount from invoice items
   */
  calculateTotal(items: any[]): number {
    return items.reduce((total, item) => total + item.amount, 0);
  }

  /**
   * Calculate the total amount from invoice payments
   */
  calculateTotalPayments(payments:any[]): number {
    return payments.reduce((total, payment) => total + payment.amount, 0);
  }
}
