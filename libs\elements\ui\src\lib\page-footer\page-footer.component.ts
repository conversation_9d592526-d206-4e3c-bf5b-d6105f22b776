import { Component, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';

import { FooterStateService } from '../services/footer-state.service';

@Component({
  selector: 'lib-page-footer',
  imports: [CommonModule],
  templateUrl: './page-footer.component.html',
  styleUrl: './page-footer.component.scss'
})
export class PageFooterComponent implements OnInit {
  activeButton: string | null = null;
  private readonly _router = inject(Router);
  private readonly _stateService = inject(FooterStateService);

  ngOnInit() {
    this.activeButton = this._stateService.getActiveButton() || 'defaultButton'; 
  }

  toggleButton(buttonName: string): void {
    this.activeButton = buttonName;
    this._stateService.saveActiveButton(buttonName);
    this.activeButton = this.activeButton === buttonName ? null : buttonName;
    switch (buttonName) {
      case 'inventory':
        this._router.navigate(['/inventory/landing']);
        break;
      case 'selling':
        this._router.navigate(['/invoices/sales']);
        break;
      case 'reports':
        this._router.navigate(['/reports']);
        break
      case 'settings':
        this._router.navigate(['/user']);
        break


    }
  }
}
