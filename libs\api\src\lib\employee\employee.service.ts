import { effect, inject, Injectable, signal, WritableSignal } from '@angular/core';
import { BaseHttpService } from '@core';
import { Observable, lastValueFrom, of, map, catchError } from 'rxjs';
import { Employee } from '@models';
import { PosProfileService } from '../pos-profile/pos-profile.service';

@Injectable({
  providedIn: 'root'
})
export class EmployeeService extends BaseHttpService<Employee> {
  private readonly _baseUrl = 'https://elevar-develop.frappe.cloud';
  private readonly _endpoint = 'api/resource/Employee';
  private readonly _posProfileService = inject(PosProfileService);

  loggedInEmployee: WritableSignal<Employee | null> = signal(null);

  constructor() {
    super();
    this.initializeService(this._endpoint, this._baseUrl);
    this.loggedInEmployee.set(JSON.parse(localStorage.getItem('loggeed-in-employee') ?? 'null'));
    effect(() => {
      if (this.loggedInEmployee()) {
        this.setPOSProfile(this.loggedInEmployee() as Employee);
      }
    })

  }

  getEmployeeByUserId(userId: string): Observable<Employee | null> {
    console.log('🏢 Searching for employee with user_id:', userId);

    // Debug cookies before making the request
    console.log('🏢 Document.cookie before employee request:', document.cookie);
    console.log('🏢 Document.cookie length:', document.cookie.length);

    const url = `${this._baseUrl}/api/resource/Employee`;
    const params = {
      filters: JSON.stringify([["user_id", "=", userId]]),
      fields: JSON.stringify(["name", "employee_name", "user_id", "company", "employee_number", "designation", "branch"])
    };

    console.log('🏢 Making request to:', url);
    console.log('🏢 Request params:', params);
    console.log('🏢 withCredentials: true');

    return this.http.get<any>(url, {
      params: params,
      withCredentials: true
    }).pipe(
      map(res => {
        console.log('🏢 Employee API response:', res);

        if (res.data && res.data.length > 0) {
          const employee = res.data[0];
          console.log('🏢 Found employee:', employee);
          // Store employee data in localStorage
          localStorage.setItem('loggeed-in-employee', JSON.stringify(employee));
          this.loggedInEmployee.set(employee);
          return employee;
        }

        console.log('🏢 No employee found for user_id:', userId);
        return null;
      }),
      catchError((err: any) => {
        console.error('🏢 Error fetching employee:', err);
        console.error('🏢 Error status:', err.status);
        console.error('🏢 Error message:', err.message);
        console.error('🏢 Error headers:', err.headers);
        console.error('🏢 Full error object:', err);

        if (err.status === 403) {
          console.error('🏢 403 Forbidden - Session cookies may not be sent or session expired');
          console.error('🏢 Current document.cookie:', document.cookie);
        }

        return of(null);
      })
    );
  }

  async setPOSProfile(employee: Employee) {
    try {
      console.log('🏪 Setting POS Profile for employee:', employee);
      const profile = await lastValueFrom(this._posProfileService.getPOSProfileForEmployeeInBranch(employee));
      console.log('🏪 POS Profile set successfully:', profile);
      return profile;
    } catch (error) {
      console.error('🏪 Error setting POS Profile:', error);
      // Don't throw the error to prevent breaking the authentication flow
      return null;
    }
  }
}
