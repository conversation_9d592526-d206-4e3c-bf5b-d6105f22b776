import { effect, inject, Injectable, signal, WritableSignal } from '@angular/core';
import { BaseHttpService } from '@core';
import { Observable, lastValueFrom, of, map, catchError } from 'rxjs';
import { Employee } from '@models';
import { PosProfileService } from '../pos-profile/pos-profile.service';

@Injectable({
  providedIn: 'root'
})
export class EmployeeService extends BaseHttpService<Employee> {
  private readonly _baseUrl = 'https://elevar-develop.frappe.cloud';
  private readonly _endpoint = 'api/resource/Employee';
  private readonly _posProfileService = inject(PosProfileService);

  loggedInEmployee: WritableSignal<Employee | null> = signal(null);

  constructor() {
    super();
    this.initializeService(this._endpoint, this._baseUrl);
    this.loggedInEmployee.set(JSON.parse(localStorage.getItem('loggeed-in-employee') ?? 'null'));
    effect(() => {
      if (this.loggedInEmployee()) {
        this.setPOSProfile(this.loggedInEmployee() as Employee);
      }
    })

  }

  getEmployeeByUserId(userId: string): Observable<Employee | null> {
    console.log('🏢 Searching for employee with user_id:', userId);

    return this.http.get<any>(
      `${this._baseUrl}/api/resource/Employee`,
      {
        params: {
          filters: JSON.stringify([["user_id", "=", userId]]),
          fields: JSON.stringify(["name", "employee_name", "user_id", "company", "employee_number", "designation", "branch"])
        },
        withCredentials: true
      }
    ).pipe(
      map(res => {
        console.log('🏢 Employee API response:', res);

        if (res.data && res.data.length > 0) {
          const employee = res.data[0];
          console.log('🏢 Found employee:', employee);
          // Store employee data in localStorage
          localStorage.setItem('loggeed-in-employee', JSON.stringify(employee));
          this.loggedInEmployee.set(employee);
          return employee;
        }

        console.log('🏢 No employee found for user_id:', userId);
        return null;
      }),
      catchError(err => {
        console.error('🏢 Error fetching employee:', err);
        return of(null);
      })
    );
  }

  async setPOSProfile(employee: Employee) {
    return await lastValueFrom(this._posProfileService.getPOSProfileForEmployeeInBranch(employee))
  }
}
