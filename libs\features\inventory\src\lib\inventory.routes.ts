import { Route } from '@angular/router';

export const routes: Route[] = [
  {
    path: 'landing',
    loadComponent: () => import('./inventory/inventory.component').then(c => c.InventoryComponent),
    title: 'Elevar - Inventory'
  },
  {
    path: 'stock-list',
    loadComponent: () => import('./stock-list/stock-list.component').then(c => c.StockListComponent),
    title: 'Elevar - Inventory'
  },
  {
    path: 'details/:id',
    loadComponent: () => import('./stock-details/stock-items-details.component').then(c => c.StockItemsDetailsComponent),
    title: 'Elevar - Inventory'
  },
  {
    path: 'request-details/:id',
    loadComponent: () => import('./material-request-details/material-req-details.component').then(c => c.MaterialReqDetailsComponent),
    title: 'Elevar - Inventory'
  },
  {
    path: 'stock-reconciliation',
    loadComponent: () => import('./stock-reconciliation/stock-reconciliation.component').then(c => c.StockReconciliationComponent),
    title: 'Elevar - Inventory'
  },
  {
    path: 'material-request',
    loadComponent: () => import('./material-request/material-request.component').then(c => c.MaterialRequestComponent),
    title: 'Elevar - Inventory'
  },
  {
    path: 'material-transfer',
    loadComponent: () => import('./material-transfer/material-transfer.component').then(c => c.MaterialTransferComponent),  
    title: 'Elevar - Inventory'
  },
  {
    path: 'new-stock-reconciliation',
    loadComponent: () => import('./new-stock-reconciliation/new-stock-reconciliation.component').then(c => c.NewStockReconciliationComponent),
    title: 'Elevar - Inventory'
  },
  {
   path: 'new-material-request',
   loadComponent: () => import('./new-material-reconciliation/new-material-request.component').then(c => c.NewMaterialRequestComponent),
   title: 'Elevar - Inventory'
  },
  {
    path: 'request-material',
    loadComponent: () => import('./request-material-request/request-form.component').then(c => c.RequestFormComponent),
    title: 'Elevar - Inventory'
  },
  {
    path: 'single-request-material-details/:id',
    loadComponent: () => import('./single-material-transfer/single-material-transfer.component').then(c => c.SingleMaterialTransferComponent),
    title: 'Elevar - Inventory'
  },

]