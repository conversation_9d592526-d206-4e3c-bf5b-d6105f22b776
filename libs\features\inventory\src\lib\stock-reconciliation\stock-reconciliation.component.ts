import { Component, inject, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';

import { Subject } from 'rxjs';

import { LoaderComponent, SharedModule } from '@elements/ui';
import { StockStore } from '@state';

@Component({
  selector: 'lib-stock-reconciliation',
  imports: [CommonModule, SharedModule, LoaderComponent],
  templateUrl: './stock-reconciliation.component.html',
  styleUrl: './stock-reconciliation.component.scss'
})
export class StockReconciliationComponent implements  OnDestroy {
  private router = inject(Router);
  stockStore = inject(StockStore)
  private readonly destroy$ = new Subject<void>();

  header = 'Stock Reconciliation';
  isLoading = false;
  searchText = '';

  navigateToNewStock() {
    this.router.navigate(['/inventory/new-stock-reconciliation']);
  }

  navigateToStockDetail(name: string) {
    this.router.navigateByUrl(`/inventory/details/${name}`);
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}