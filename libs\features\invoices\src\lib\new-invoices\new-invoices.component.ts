import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { ButtonComponent, ElevarInputsComponent, PageFooterComponent, PageHeaderComponent } from '@elements/ui';
import { Subject, BehaviorSubject, debounceTime, distinctUntilChanged, map, switchMap, tap, finalize } from 'rxjs';
import { CartItem, InventoryItem } from '@models';
import { EditCartItemComponent } from "../edit-cart-item/edit-cart-item.component";
import { CartService, InventoryService, PosProfileService } from '@elevar-clients/api';

@Component({
  selector: 'lib-new-invoices',
  standalone: true,
  imports: [
    CommonModule,
    PageHeaderComponent,
    ElevarInputsComponent,
    PageFooterComponent,
    ButtonComponent,
    FormsModule,
    ReactiveFormsModule,
    EditCartItemComponent
  ],
  templateUrl: './new-invoices.component.html',
  styleUrl: './new-invoices.component.scss'
})
export class NewInvoicesComponent {
  header = 'New Sales Invoices';
  private readonly _route = inject(Router);
  private readonly _inventoryService = inject(InventoryService);
  private readonly _posService = inject(PosProfileService);
  readonly cart = inject(CartService);

  currentWarehouse = this._posService.posProfile()?.warehouse;

  private readonly searchResultsSubject = new BehaviorSubject<InventoryItem[]>([]);
  allItems: InventoryItem[] = [];
  searchResults$ = this.searchResultsSubject.asObservable();
  selectedItem: any | null = null;
  isLoading = false;
  showEditModal = false;
  editingItem: CartItem | null = null;
  availableStock = 0;
  availableQty = 0;
  quantityWarning = '';

  formGroup = new FormGroup({
    search: new FormControl('', [Validators.required]),
    quantity: new FormControl('0', [Validators.required, Validators.min(1)])
  });

  constructor() {
    this.isLoading = true;
    this._inventoryService.getWareHouseInventoryWithPrices(this.currentWarehouse as string).pipe(
      finalize(() => this.isLoading = false)
    ).subscribe(items => {
      this.allItems = items;
      this.searchResultsSubject.next(this.allItems);
    });

    this.searchControl.valueChanges.subscribe(value => {
      if (this.selectedItem && value !== this.selectedItem.item_code) {
        this.selectedItem = null;
        this.searchControl.setValue('', { emitEvent: false });
        this.searchResultsSubject.next(this.allItems);
        return;
      }
      if (value && !this.selectedItem) {
        const filtered = this.allItems.filter(item =>
          item.item_code.toLowerCase().includes(value.toLowerCase())
        );
        this.searchResultsSubject.next(filtered);
      } else if (!value) {
        this.searchResultsSubject.next(this.allItems);
      }
    });
  }

  handleAddItem() {
    if (this.formGroup.valid && this.selectedItem) {
      // If item exists in cart, start editing instead
      if (this.cart.hasItem(this.selectedItem.item_code)) {
        const existingItem = this.cart.getItem(this.selectedItem.item_code);
        if (existingItem) {
          this.startEditing(this.selectedItem.item_code);
          this.resetForm();
          return;
        }
      }

      // Add new item to cart
      this.cart.addItem(
        this.selectedItem,
        Number(this.quantityControl.value)
      );
      this.resetForm();
    }
  }

  startEditing(itemCode: string) {
    const item = this.cart.getItem(itemCode);
    if (item) {
      this.editingItem = item;
      this.showEditModal = true;
    }
  }

  handleEditSave(updatedItem: CartItem) {
    this.cart.updateItem(updatedItem);
    this.showEditModal = false;
    this.editingItem = null;
  }

  clearCart(evt:boolean){
    this.cart.clearCart();
    this.resetForm();
  }

  resetForm() {
    this.formGroup.reset({
      search: '',
      quantity: '0'
    });
    this.selectedItem = null;
    this.clearSearchResults();
  }

  clearSearchResults() {
    this.searchResultsSubject.next(this.allItems);
  }

  selectItem(item: InventoryItem) {
    this.selectedItem = item;
    this.searchControl.setValue(item.item_code, { emitEvent: false });
    this.quantityControl.setValidators([
      Validators.required,
      Validators.min(1),
      Validators.max(item.available_qty ?? item.actual_qty)
    ])
    this.availableStock = item.actual_qty;
    this.availableQty = item.available_qty ?? item.actual_qty;
    this.searchResultsSubject.next([]);
  }

  validateQuantity() {
    if (this.selectedItem && this.quantityControl.value > (this.selectedItem.available_qty ?? this.availableStock)) {
      this.quantityControl.setErrors({ stockExceeded: true });
      this.quantityWarning = `Quantity cannot exceed available stock (${this.selectedItem.available_qty ?? this.availableStock}).`;
    } else {
      this.quantityControl.setErrors(null);
      this.quantityWarning = '';
    }
  }

  get searchControl(): FormControl {
    return this.formGroup.get('search') as FormControl;
  }

  get quantityControl(): FormControl {
    return this.formGroup.get('quantity') as FormControl;
  }

  isSelector(formControl: string) {
    return formControl.includes('selected');
  }

  isPassword(formControl: string) {
    return formControl.includes('password');
  }

  navigateToAddInvoice() {
    if (!this.isNextButtonDisabled) {
      this._route.navigate(['/invoices/add-payment']);
    }
  }

  get isNextButtonDisabled(): boolean {
    // Prevent next if any cart item exceeds available_qty
    const cartHasInvalidQty = this.cart.items().some(item => {
      const maxQty = (item as any)?.available_qty ?? (item as any)?.actual_qty ?? Infinity;
      return item.quantity > maxQty;
    });
    return this.cart.isEmpty() || cartHasInvalidQty;
  }

  get isAddItemDisabled(): boolean {
    if (!this.selectedItem || !this.formGroup.valid) return true;
    // Disable if entered quantity exceeds available_qty
    return this.quantityControl.value > (this.selectedItem.available_qty ?? this.availableStock);
  }

  get addItemButtonType(): 'loading' | 'disabled' | 'default' {
    if (this.isLoading) return 'loading';
    if (this.isAddItemDisabled) return 'disabled';
    return 'default';
  }

  get cartHasInvalidQty(): boolean {
    return this.cart.items().some(item => {
      const maxQty = (item as any)?.available_qty ?? (item as any)?.actual_qty ?? Infinity;
      return item.quantity > maxQty;
    });
  }
}
