import { Injectable, computed, effect, inject, linkedSignal, signal } from '@angular/core';
import { NotificationService } from '@core';
import { CartItem, Item, MaterialRequestType } from '@models';

@Injectable({
  providedIn: 'root'
})
export class MaterialCartService {
  private readonly STORAGE_KEY = 'material_items';
  private readonly MATERIAL_REQUEST_TYPE = 'material_request_type';

  private readonly _notificationService = inject(NotificationService)

  private readonly materialItems = signal<any[]>(this.loadCartFromStorage());
  private readonly _materialRequestType = signal<MaterialRequestType>(this.loadMaterialRequestTypeFromStorage());

  // Computed signals for derived state
  readonly materialRequestType = linkedSignal(() => this._materialRequestType());
  readonly items = linkedSignal(() => this.materialItems());
  readonly totalItems = linkedSignal(() => this.materialItems().length);
  readonly isEmpty = linkedSignal(() => this.totalItems() === 0);

  constructor() {
    // Effect for persisting to localStorage whenever cart changes
    effect(() => {
      const currentItems = this.materialItems();
      this.saveCartToStorage(currentItems);
    });
  }

  updateMaterialRequestType(materialRequestType: MaterialRequestType) {
    this._materialRequestType.set(materialRequestType);
    localStorage.setItem(this.MATERIAL_REQUEST_TYPE, JSON.stringify(materialRequestType));
  }

  clearMaterialRequestType() {
    this._materialRequestType.set(MaterialRequestType.PURCHASE);
    localStorage.removeItem(this.MATERIAL_REQUEST_TYPE);
  }

  /**
   * Add or update item in cart
   */
  addItem(item: Item, quantity: number): void {
    const currentItems = this.materialItems();
    const existingItemIndex = currentItems.findIndex(
      cartItem => cartItem.item_code === item.item_code
    );

    if (existingItemIndex !== -1) {
      // Update existing item
      this.materialItems.update(items => items.map((cartItem, index) =>
        index === existingItemIndex
          ? { ...cartItem, quantity }
          : cartItem
      ));
    } else {
      // Add new item
      const newCartItem: CartItem = {
        ...item,
        quantity,
        isEditing: false
      };
      this.materialItems.update(items => [...items, newCartItem]);
    }

    this._notificationService.setSuccess('Item added', `Item ${item.item_code} added to the Material Request`)
  }

  /**
   * Update an existing cart item
   */
  updateItem(updatedItem: CartItem): void {
    this.materialItems.update(items =>
      items.map(item =>
        item.item_code === updatedItem.item_code
          ? { ...updatedItem, isEditing: false, editQuantity: undefined }
          : item
      )
    );

    this._notificationService.setSuccess('Item updated', `Item: ${updatedItem.item_code} updated in the Material Request`)
  }

  /**
   * Remove an item from cart
   */
  removeItem(itemCode: string): void {
    this.materialItems.update(items =>
      items.filter(item => item.item_code !== itemCode)
    );

    this._notificationService.setSuccess('Item removed', `Item: ${itemCode} removed from the Material Request`)
  }

  /**
   * Clear all items from cart
   */
  clearCart(): void {
    this.materialItems.set([]);
    this._notificationService.setWarning('Material Request cleared', 'Items have been removed from the Material Request')
  }

  /**
   * Start editing an item
   */
  startEditing(itemCode: string): void {
    this.materialItems.update(items =>
      items.map(item => ({
        ...item,
        isEditing: item.item_code === itemCode,
        editQuantity: item.item_code === itemCode ? item.quantity : undefined
      }))
    );
  }

  /**
   * Cancel editing for an item
   */
  cancelEditing(itemCode: string): void {
    this.materialItems.update(items =>
      items.map(item => ({
        ...item,
        isEditing: false,
        editQuantity: undefined
      }))
    );
  }

  /**
   * Update edit quantity for an item
   */
  updateEditQuantity(itemCode: string, quantity: number): void {
    if (quantity <= 0) return;

    this.materialItems.update(items =>
      items.map(item =>
        item.item_code === itemCode
          ? { ...item, editQuantity: quantity }
          : item
      )
    );
  }

  /**
   * Save edited quantity for an item
   */
  saveEditing(itemCode: string): void {
    this.materialItems.update(items =>
      items.map(item => {
        if (item.item_code === itemCode && item.editQuantity && item.editQuantity > 0) {
          return {
            ...item,
            quantity: item.editQuantity,
            isEditing: false,
            editQuantity: undefined
          };
        }
        return item;
      })
    );

    this._notificationService.setSuccess('Item updated', `Item ${itemCode} updated in the Material Request`)
  }

  /**
   * Get a specific item from cart
   */
  getItem(itemCode: string): CartItem | undefined {
    return this.materialItems().find(item => item.item_code === itemCode);
  }

  /**
   * Check if an item exists in cart
   */
  hasItem(itemCode: string): boolean {
    return this.materialItems().some(item => item.item_code === itemCode);
  }

  /**
   * Load cart from localStorage
   */
  private loadCartFromStorage(): CartItem[] {
    try {
      const storedCart = localStorage.getItem(this.STORAGE_KEY);
      return storedCart ? JSON.parse(storedCart) : [];
    } catch (error) {
      console.error('Error loading cart from localStorage:', error);
      return [];
    }
  }

  private loadMaterialRequestTypeFromStorage(): MaterialRequestType {
    try {
      const storedMaterialRequestType = localStorage.getItem(this.MATERIAL_REQUEST_TYPE);
      return storedMaterialRequestType ? JSON.parse(storedMaterialRequestType) : MaterialRequestType.PURCHASE;
    } catch (error) {
      console.error('Error loading material request type from localStorage:', error);
      return MaterialRequestType.PURCHASE;
    }
  }

  /**
   * Save cart to localStorage
   */
  private saveCartToStorage(items: CartItem[]): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(items));
    } catch (error) {
      console.error('Error saving cart to localStorage:', error);
    }
  }
}