{"name": "@elevar-clients/source", "version": "0.0.0", "license": "MIT", "scripts": {"start": "nx run pos-client:serve:development", "start:client": "nx run pos-client:serve:development", "build:client": "nx run pos-client:build:production"}, "private": true, "dependencies": {"@angular/animations": "~19.0.0", "@angular/common": "~19.0.0", "@angular/compiler": "~19.0.0", "@angular/core": "~19.0.0", "@angular/fire": "^19.2.0", "@angular/forms": "~19.0.0", "@angular/platform-browser": "~19.0.0", "@angular/platform-browser-dynamic": "~19.0.0", "@angular/router": "~19.0.0", "@angular/service-worker": "~19.0.0", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@ngrx-traits/signals": "^19.0.0-beta.8", "@ngrx/signals": "^19.0.1", "angular-svg-icon": "^19.0.1", "firebase": "^11.8.1", "lodash": "^4.17.21", "lodash.sortby": "^4.7.0", "material-icons": "^1.13.14", "ngx-cookie-service": "^19.0.0", "ngx-sonner": "^3.0.0", "rxjs": "~7.8.0", "sonner": "^1.7.1", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "~19.0.0", "@angular-devkit/core": "~19.0.0", "@angular-devkit/schematics": "~19.0.0", "@angular/cli": "~19.0.0", "@angular/compiler-cli": "~19.0.0", "@angular/language-service": "~19.0.0", "@angular/pwa": "^19.0.5", "@eslint/js": "^9.8.0", "@nx/angular": "20.2.2", "@nx/devkit": "20.2.2", "@nx/eslint": "20.2.2", "@nx/eslint-plugin": "20.2.2", "@nx/jest": "20.2.2", "@nx/js": "20.2.2", "@nx/playwright": "20.2.2", "@nx/web": "20.2.2", "@nx/workspace": "20.2.2", "@playwright/test": "^1.36.0", "@schematics/angular": "~19.0.0", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.17", "@types/node": "18.16.9", "@typescript-eslint/utils": "^8.13.0", "angular-eslint": "^19.0.2", "autoprefixer": "^10.4.20", "eslint": "^9.8.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-playwright": "^1.6.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-preset-angular": "~14.4.0", "jsonc-eslint-parser": "^2.1.0", "ng-packagr": "~19.0.0", "nx": "20.2.2", "postcss": "^8.4.49", "postcss-url": "~10.1.3", "prettier": "^2.8.8", "prettier-plugin-tailwindcss": "^0.6.9", "tailwindcss": "^3.4.17", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "tslib": "^2.3.0", "typescript": "~5.6.2", "typescript-eslint": "^8.13.0"}}