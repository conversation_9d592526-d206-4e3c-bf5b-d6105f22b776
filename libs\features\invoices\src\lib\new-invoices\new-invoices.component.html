<lib-page-header [header]="header" (performActionOnBack)="clearCart($event)" [customBackUrl]="'invoices'" [showArrowIcon]="true" [isSearchable]="false" />

<main class="flex-grow p-4 overflow-y-auto">
  <form [formGroup]="formGroup" class="mb-6" (ngSubmit)="handleAddItem()">
    <div class="relative mb-4">
      <label for="item" class="block text-base font-light text-gray-700 mb-1">Item</label>
      <lib-elevar-inputs
        [placeholder]="'Enter Item Name'"
        [control]="searchControl"
        [inputId]="'search'"
        [label]="'search'"
        [type]="isPassword('search') ? 'password' : isSelector('search') ? 'selector' : 'text'">
      </lib-elevar-inputs>

      <!-- Search Results Dropdown -->
      @if (!selectedItem && searchControl.value && (searchResults$ | async); as results) {
        <div class="absolute z-10 w-full bg-gray-200 shadow-lg rounded-md -mt-4 max-h-60 overflow-y-auto">
          @if (results.length > 0) {
            @for (item of results; track item.item_code) {
              <button
                type="button"
                class="w-full text-left px-4 py-2 hover:bg-gray-100 cursor-pointer"
                (click)="selectItem(item)"
                (keydown.enter)="selectItem(item)">
                {{item.item_code | titlecase}}
              </button>
            }
          } @else {
            <div class="flex flex-col items-center justify-center py-6 px-4 text-center text-gray-500">
              <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto mb-2 h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              <span class="font-medium">No items found</span>
              <span class="text-xs">Your search did not match any items.</span>
            </div>
          }
        </div>
      }
    </div>

    <div class="mb-4">
      <div class="flex justify-between items-center">
        <label for="item" class="text-base font-light text-gray-700">Quantity</label>
        <label for="item" class="text-xs font-light text-gray-700">KG</label>
      </div>
      <lib-elevar-inputs
        [control]="quantityControl"
        [inputId]="'quantity'"
        [label]="'quantity'"
        [type]="'number'"
        (change)="validateQuantity()"
        (input)="validateQuantity()">
      </lib-elevar-inputs>
      @if (quantityControl.hasError('stockExceeded')) {
          <div id="password-error-1" class="flex items-center gap-2 text-sm font-medium text-red-500">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
              <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z" />
            </svg>
            <span>Quantity cannot exceed available stock ({{ availableQty }}).</span>
          </div>
      }
      @if (quantityWarning) {
        <div class="flex items-center gap-2 text-sm font-medium text-red-500 mt-1">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-5">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z" />
          </svg>
          <span>{{ quantityWarning }}</span>
        </div>
      }
    </div>

    <!-- Add Item Button -->
    <lib-button
      type="submit"
      [buttonType]="addItemButtonType"
      class="w-2/5 block">
      Add Item
    </lib-button>
  </form>

  <div class="rounded-md overflow-hidden mb-4">
    <div class="overflow-x-auto rounded-lg border border-gray-200 shadow-sm">
      <table class="min-w-full divide-y divide-gray-200">
        <thead>
          <tr class="bg-primary-greenLight">
            <th class="whitespace-nowrap px-4 py-3.5 text-center text-sm font-medium text-white w-1/4">
              Item Name
            </th>
            <th class="whitespace-nowrap px-4 py-3.5 text-center text-sm font-medium text-white w-1/4">
              Amount
            </th>
            <th class="whitespace-nowrap px-4 py-3.5 text-center text-sm font-medium text-white w-1/4">
              Qty
            </th>
            <th class="whitespace-nowrap px-4 py-3.5 text-center text-sm font-medium text-white w-1/4">
              Action
            </th>
          </tr>
        </thead>
        <tbody class="divide-y divide-gray-200 bg-white">
          @if (cart.isEmpty()) {
            <tr>
              <td colspan="4" class="px-4 py-8 text-center text-sm text-gray-500">
                <div class="flex flex-col items-center justify-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                  <span>No items in invoice</span>
                </div>
              </td>
            </tr>
          }
          @for (item of cart.items(); track item.item_code) {
            <tr class="hover:bg-gray-50 transition-colors">
              <td class="whitespace-nowrap px-2 py-4 text-sm text-center">
                <div class="flex flex-col">
                  <span class="font-medium text-gray-900">{{item.item_code | titlecase}}</span>
                </div>
              </td>

              <td class="whitespace-nowrap px-2 py-4 text-sm text-gray-600 text-center">
                <span class="inline-flex items-center rounded-full bg-green-50 px-2 py-1 text-xs font-medium text-green-700">
                  KES {{item.price}}
                </span>
              </td>

              <td class="whitespace-nowrap px-2 py-4 text-sm text-gray-600 text-center">
                <div class="flex items-center justify-center">
                  <span class="inline-flex items-center rounded-full bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700">
                    {{item.quantity}} KG
                  </span>
                </div>
              </td>

              <td class="whitespace-nowrap px-2 py-4 text-sm text-center">
                <button
                  type="button"
                  class="inline-flex items-center justify-center w-8 h-8 rounded-full hover:bg-gray-100 cursor-pointer transition-colors"
                  (click)="startEditing(item.item_code)">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                  </svg>
                </button>
              </td>
            </tr>
          }
        </tbody>
      </table>
    </div>
  </div>

  @if (cartHasInvalidQty) {
    <div class="flex items-center gap-2 p-2 mb-2 rounded border border-[#ffcc00] bg-[#ffcc00]/20 text-[#332800] text-xs font-medium">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="#b28e00">
        <circle cx="12" cy="12" r="10" stroke="#b28e00" stroke-width="2" fill="#ffcc00"/>
        <path stroke="#332800" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" d="M12 8v4m0 4h.01" />
      </svg>
      <span>One or more items in your cart exceed the available stock. Please adjust the quantities before proceeding.</span>
    </div>
  }

  <!-- Next Button -->
  <lib-button
    type="button"
    [buttonType]="isNextButtonDisabled ? 'disabled' : 'default'"
    class="w-2/5 block"
    (click)="navigateToAddInvoice()">
    <div class="flex items-center justify-center">
      <span>Next</span>
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2 transform rotate-180" fill="none" viewBox="0 0 24 24"
        stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
      </svg>
    </div>
  </lib-button>

  @if (showEditModal && editingItem) {
    <lib-edit-cart-item
      [item]="editingItem"
      [type]="'cartItem'"
      (save)="handleEditSave($event)"
      (close)="showEditModal = false">
    </lib-edit-cart-item>
  }
</main>

<lib-page-footer />
