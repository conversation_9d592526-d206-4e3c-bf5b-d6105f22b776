import { effect, inject, Injectable, signal, WritableSignal } from '@angular/core';
import { HttpHeaders } from '@angular/common/http';
import { BaseHttpService } from '@core';
import { lastValueFrom, Observable, of, switchMap, tap } from 'rxjs';
import { Employee, LoginRequest, LoginResponse, User } from '@models';
import { EmployeeService } from '../employee/employee.service';
import { CookieService } from 'ngx-cookie-service';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class AuthService extends BaseHttpService<unknown> {
  private readonly _employeeService = inject(EmployeeService);
  private readonly _cookieService = inject(CookieService);
  private readonly _router = inject(Router);

  private readonly _authBaseUrl = 'https://elevar-develop.frappe.cloud/api/method';

  isLoggedIn: WritableSignal<boolean> = signal(false);
  loggedInUser: WritableSignal<User | null> = signal(null);

  constructor() {
    super();
    this.initializeService('login', this._authBaseUrl);
    
    // Load user data from localStorage only once
    const storedUser = localStorage.getItem('current-user');
    if (storedUser) {
      try {
        this.loggedInUser.set(JSON.parse(storedUser));
      } catch (e) {
        console.error('Error parsing stored user', e);
        localStorage.removeItem('current-user');
      }
    }
    
    const loggedInEmployee = JSON.parse(localStorage.getItem('loggeed-in-employee') ?? 'null');
    
    if (!!loggedInEmployee && !!this.loggedInUser()) {
      this.isLoggedIn.set(true);
    } else {
      this.isLoggedIn.set(false);
    }

    effect(() => {
      if (this.isLoggedIn()) {
        this.setEmployeeDetails(this.loggedInUser()?.user_id as string);
      }
    });
  }

  // Override getHeaders for login-specific content type
  protected override getHeaders(): HttpHeaders {
    return new HttpHeaders({
      'Content-Type': 'application/x-www-form-urlencoded'
    });
  }

  login(credentials: LoginRequest): Observable<Employee | null> {
    const formData = new URLSearchParams();
    formData.set('usr', credentials.usr);
    formData.set('pwd', credentials.pwd);

    // Clear any existing cookies before login to prevent duplication
    this._cookieService.deleteAll('/');
    localStorage.removeItem('current-user');

    return this.http.post<LoginResponse>(
      this.apiUrl,
      formData.toString(),
      {
        headers: this.getHeaders(),
        withCredentials: true
      }
    ).pipe(switchMap(res => {
      if (res.message === 'Logged In') {
        // The cookie interceptor will have stored the cookies in localStorage
        // We can now use them to set the logged-in user
        const rawCurrentUser = localStorage.getItem('current-user');
        if (rawCurrentUser) {
          const currentUser: User = JSON.parse(rawCurrentUser);
          this.loggedInUser.set(currentUser);
          this.isLoggedIn.set(true);
          return this.getEmployeeDetails(currentUser.user_id);
        }
      }
      return of(null);
    }));
  }

  async setEmployeeDetails(id: string) {
    return await lastValueFrom(this.getEmployeeDetails(id));
  }

  logout(): Observable<unknown> {
    return this.http.get(
      `${this.apiUrl}/logout`,
      {
        headers: this.getHeaders(),
        withCredentials: true
      }
    ).pipe(
      tap(() => {
        this.isLoggedIn.set(false);
        this.loggedInUser.set(null);
        localStorage.clear();
        this._cookieService.deleteAll('/');
        this._router.navigate(['/auth/login']);
      })
    );
  }

  getEmployeeDetails(user_id: string) {
    return this._employeeService.getEmployeeByUserId(user_id);
  }
}
