import { effect, inject, Injectable, isDevMode, signal, WritableSignal } from '@angular/core';
import { HttpHeaders } from '@angular/common/http';
import { BaseHttpService } from '@core';
import { catchError, lastValueFrom, Observable, of, switchMap, tap, timer } from 'rxjs';
import { Employee, LoginRequest, LoginResponse, User } from '@models';
import { EmployeeService } from '../employee/employee.service';
import { CookieService } from 'ngx-cookie-service';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class AuthService extends BaseHttpService<unknown> {
  private readonly _employeeService = inject(EmployeeService);
  private readonly _cookieService = inject(CookieService);
  private readonly _router = inject(Router);

  private readonly _authBaseUrl = 'https://elevar-develop.frappe.cloud/api/method';

  isLoggedIn: WritableSignal<boolean> = signal(false);
  loggedInUser: WritableSignal<User | null> = signal(null);

  constructor() {
    super();
    this.initializeService('login', this._authBaseUrl);
    
    // Load user data from localStorage only once
    const storedUser = localStorage.getItem('current-user');
    if (storedUser) {
      try {
        this.loggedInUser.set(JSON.parse(storedUser));
      } catch (e) {
        console.error('Error parsing stored user', e);
        localStorage.removeItem('current-user');
      }
    }
    
    const loggedInEmployee = JSON.parse(localStorage.getItem('loggeed-in-employee') ?? 'null');
    
    if (!!loggedInEmployee && !!this.loggedInUser()) {
      this.isLoggedIn.set(true);
    } else {
      this.isLoggedIn.set(false);
    }

    effect(() => {
      if (this.isLoggedIn()) {
        this.setEmployeeDetails(this.loggedInUser()?.user_id as string);
      }
    });
  }

  // Override getHeaders for login-specific content type
  protected override getHeaders(): HttpHeaders {
    return new HttpHeaders({
      'Content-Type': 'application/x-www-form-urlencoded'
    });
  }

  login(credentials: LoginRequest): Observable<Employee | null> {
    console.log('🚀🚀🚀 AUTH SERVICE LOGIN METHOD CALLED 🚀🚀🚀');
    console.log('🚀 Login method called with credentials:', { usr: credentials.usr });
    console.log('🚀 API URL:', this.apiUrl);
    console.log('🚀 this.http exists:', !!this.http);

    console.log('🚀 Creating form data...');
    const formData = new URLSearchParams();
    formData.set('usr', credentials.usr);
    formData.set('pwd', credentials.pwd);

    console.log('🚀 Form data prepared:', formData.toString());

    // Clear existing data
    console.log('🚀 Clearing auth data...');
    try {
      this.clearAuthData();
      console.log('🚀 Auth data cleared successfully');
    } catch (error) {
      console.error('🚨 Error clearing auth data:', error);
    }

    console.log('🚀 Making HTTP POST request...');

    console.log('🚀 About to make HTTP POST with:', {
      url: this.apiUrl,
      body: formData.toString(),
      headers: this.getHeaders(),
      withCredentials: true
    });

    return this.http.post<LoginResponse>(
      this.apiUrl,
      formData.toString(),
      {
        headers: this.getHeaders(),
        withCredentials: true
      }
    ).pipe(
      tap(res => {
        console.log('🚀 HTTP POST completed successfully');
        console.log('🚀 Login response:', res);
      }),
      switchMap(res => {
        console.log('🚀 Processing login response in switchMap:', res);

        if (res.message === 'Logged In') {
          console.log('🚀 Login message confirmed, calling extractUserFromCookies...');
          // ERPNext sets cookies automatically, we need to extract user info from cookies
          return this.extractUserFromCookies().pipe(
            switchMap(user => {
              console.log('🚀 User from extractUserFromCookies:', user);

              if (user) {
                this.loggedInUser.set(user);
                console.log('🚀 Getting employee details for user_id:', user.user_id);
                return this.getEmployeeDetails(user.user_id);
              }

              console.log('🚀 No user extracted from cookies');
              return of(null);
            })
          );
        } else {
          console.log('🚀 Login message is not "Logged In", got:', res.message);
        }
        return of(null);
      }),
      tap(employee => {
        if (employee) {
          this.isLoggedIn.set(true);
          if (isDevMode()) {
            console.log('✅ Login successful, employee found:', employee);
          }
        } else {
          if (isDevMode()) {
            console.log('❌ Login failed or no employee found');
          }
        }
      }),
      catchError(error => {
        if (isDevMode()) {
          console.error('🚨 Login error:', error);
        }
        return of(null);
      })
    );
  }

  async setEmployeeDetails(id: string) {
    return await lastValueFrom(this.getEmployeeDetails(id));
  }

  logout(): Observable<unknown> {
    return this.http.get(
      `${this.apiUrl}/logout`,
      {
        headers: this.getHeaders(),
        withCredentials: true
      }
    ).pipe(
      tap(() => {
        this.clearAuthData();
        this._router.navigate(['/auth/login']);
      })
    );
  }

  getEmployeeDetails(user_id: string) {
    return this._employeeService.getEmployeeByUserId(user_id);
  }

  /**
   * Clear all authentication data
   */
  private clearAuthData(): void {
    // Clear localStorage
    localStorage.removeItem('current-user');
    localStorage.removeItem('loggeed-in-employee');

    // Clear cookies more thoroughly
    const domain = 'elevar-develop.frappe.cloud';
    const cookieNames = ['sid', 'user_id', 'full_name', 'system_user', 'user_image'];

    cookieNames.forEach(name => {
      this._cookieService.delete(name, '/');
      this._cookieService.delete(name, '/', domain);
      this._cookieService.delete(name, '/', `.${domain}`);
      // Also try without domain
      this._cookieService.delete(name);
    });

    // Reset signals
    this.isLoggedIn.set(false);
    this.loggedInUser.set(null);
  }

  /**
   * Extract user information from ERPNext cookies
   */
  private extractUserFromCookies(): Observable<User | null> {
    console.log('🍪🍪🍪 EXTRACT USER FROM COOKIES CALLED 🍪🍪🍪');

    // Give cookies time to be set by the browser
    console.log('🍪 Starting timer...');
    return timer(100).pipe(
      tap(() => {
        console.log('🍪 Timer emitted, about to enter switchMap...');
      }),
      switchMap(() => {
        console.log('🍪 Timer completed, getting cookies...');
        console.log('🍪 Cookie service exists:', !!this._cookieService);

        const cookies = this._cookieService.getAll();
        console.log('🍪 Raw cookies from service:', cookies);

        console.log('🍪 All cookies:', cookies);
        console.log('🍪 Number of cookies:', Object.keys(cookies).length);

        // Filter out Guest and empty cookies
        const validCookies = Object.keys(cookies).reduce((acc, key) => {
          const value = cookies[key];
          console.log(`🍪 Processing cookie "${key}": "${value}"`);
          if (value && value !== 'Guest' && value.trim() !== '') {
            acc[key] = value;
            console.log(`🍪 ✅ Added valid cookie "${key}": "${value}"`);
          } else {
            console.log(`🍪 ❌ Rejected cookie "${key}": "${value}"`);
          }
          return acc;
        }, {} as Record<string, string>);

        console.log('🍪 Valid cookies after filtering:', validCookies);
        console.log('🍪 Number of valid cookies:', Object.keys(validCookies).length);

        // Check if we have essential ERPNext cookies
        if (validCookies['sid'] && validCookies['user_id'] && validCookies['user_id'] !== 'Guest') {
          const user: User = {
            user_id: decodeURIComponent(validCookies['user_id']),
            full_name: validCookies['full_name'] ? decodeURIComponent(validCookies['full_name']) : '',
            system_user: (validCookies['system_user'] === 'yes') ? 'yes' : 'no',
            user_image: validCookies['user_image'] || ''
          };

          // Store in localStorage for persistence
          localStorage.setItem('current-user', JSON.stringify(user));

          if (isDevMode()) {
            console.log('User extracted from cookies:', user);
            console.log('Stored in localStorage:', localStorage.getItem('current-user'));
          }

          return of(user);
        } else {
          if (isDevMode()) {
            console.error('Missing essential cookies:', {
              sid: validCookies['sid'],
              user_id: validCookies['user_id'],
              allValidCookies: validCookies
            });
          }
          return of(null);
        }
      })
    );
  }
}
