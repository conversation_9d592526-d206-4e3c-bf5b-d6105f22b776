import { effect, inject, Injectable, isDevMode, signal, WritableSignal } from '@angular/core';
import { HttpHeaders } from '@angular/common/http';
import { BaseHttpService } from '@core';
import { from, lastValueFrom, Observable, of, switchMap, tap } from 'rxjs';
import { Employee, LoginRequest, LoginResponse, User } from '@models';
import { EmployeeService } from '../employee/employee.service';
import { CookieService } from 'ngx-cookie-service';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root'
})
export class AuthService extends BaseHttpService<unknown> {
  private readonly _employeeService = inject(EmployeeService);
  private readonly _cookieService = inject(CookieService);
  private readonly _router = inject(Router);

  private readonly _authBaseUrl = 'https://elevar-develop.frappe.cloud/api/method';

  isLoggedIn: WritableSignal<boolean> = signal(false);
  loggedInUser: WritableSignal<User | null> = signal(null);

  constructor() {
    super();
    this.initializeService('login', this._authBaseUrl);
    
    // Load user data from localStorage only once
    const storedUser = localStorage.getItem('current-user');
    if (storedUser) {
      try {
        this.loggedInUser.set(JSON.parse(storedUser));
      } catch (e) {
        console.error('Error parsing stored user', e);
        localStorage.removeItem('current-user');
      }
    }
    
    const loggedInEmployee = JSON.parse(localStorage.getItem('loggeed-in-employee') ?? 'null');
    
    if (!!loggedInEmployee && !!this.loggedInUser()) {
      this.isLoggedIn.set(true);
    } else {
      this.isLoggedIn.set(false);
    }

    effect(() => {
      if (this.isLoggedIn()) {
        this.setEmployeeDetails(this.loggedInUser()?.user_id as string);
      }
    });
  }

  // Override getHeaders for login-specific content type
  protected override getHeaders(): HttpHeaders {
    return new HttpHeaders({
      'Content-Type': 'application/x-www-form-urlencoded'
    });
  }

  login(credentials: LoginRequest): Observable<Employee | null> {
    const formData = new URLSearchParams();
    formData.set('usr', credentials.usr);
    formData.set('pwd', credentials.pwd);

    // Clear any existing cookies before login to prevent duplication
    // Clear cookies for different paths and the current domain
    const domain = 'elevar-develop.frappe.cloud';
    const cookieNames = ['sid', 'user_id', 'full_name', 'system_user', 'user_image'];

    cookieNames.forEach(name => {
      this._cookieService.delete(name, '/');
      this._cookieService.delete(name, '/', domain);
      this._cookieService.delete(name, '/', `.${domain}`);
    });

    localStorage.removeItem('current-user');
    localStorage.removeItem('loggeed-in-employee');

    return this.http.post<LoginResponse>(
      this.apiUrl,
      formData.toString(),
      {
        headers: this.getHeaders(),
        withCredentials: true
      }
    ).pipe(switchMap(res => {
      if (res.message === 'Logged In') {
        // The cookie interceptor will have stored the cookies in localStorage with a delay
        // We need to wait a bit longer to ensure the data is available
        return from(new Promise<Employee | null>((resolve) => {
          setTimeout(() => {
            const rawCurrentUser = localStorage.getItem('current-user');
            if (isDevMode()) {
              console.log('Raw current user from localStorage:', rawCurrentUser);
            }

            if (rawCurrentUser) {
              try {
                const currentUser: User = JSON.parse(rawCurrentUser);
                if (isDevMode()) {
                  console.log('Parsed current user:', currentUser);
                  console.log('Fetching employee details for user_id:', currentUser.user_id);
                }

                this.loggedInUser.set(currentUser);
                this.isLoggedIn.set(true);
                this.getEmployeeDetails(currentUser.user_id).subscribe(employee => {
                  if (isDevMode()) {
                    console.log('Employee details result:', employee);
                  }
                  resolve(employee);
                });
              } catch (error) {
                console.error('Error parsing current user:', error);
                resolve(null);
              }
            } else {
              console.error('No current user found in localStorage after login');
              resolve(null);
            }
          }, 200); // Wait a bit longer than the interceptor
        }));
      }
      return of(null);
    }));
  }

  async setEmployeeDetails(id: string) {
    return await lastValueFrom(this.getEmployeeDetails(id));
  }

  logout(): Observable<unknown> {
    return this.http.get(
      `${this.apiUrl}/logout`,
      {
        headers: this.getHeaders(),
        withCredentials: true
      }
    ).pipe(
      tap(() => {
        this.isLoggedIn.set(false);
        this.loggedInUser.set(null);
        localStorage.clear();

        // Clear cookies more thoroughly
        const domain = 'elevar-develop.frappe.cloud';
        const cookieNames = ['sid', 'user_id', 'full_name', 'system_user', 'user_image'];

        cookieNames.forEach(name => {
          this._cookieService.delete(name, '/');
          this._cookieService.delete(name, '/', domain);
          this._cookieService.delete(name, '/', `.${domain}`);
        });

        this._router.navigate(['/auth/login']);
      })
    );
  }

  getEmployeeDetails(user_id: string) {
    return this._employeeService.getEmployeeByUserId(user_id);
  }
}
