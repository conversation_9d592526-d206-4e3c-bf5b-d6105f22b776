import { inject } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  CanActivateChildFn,
  Router,
  RouterStateSnapshot,
} from '@angular/router';
import { CookieService } from 'ngx-cookie-service';

export const isLoggedInGuard: CanActivateChildFn = (
  route: ActivatedRouteSnapshot,
  state: RouterStateSnapshot
) => {
  const router = inject(Router);
  const cookieService = inject(CookieService);
  
  // Check for ERPNext cookies
  const currentUser = JSON.parse(localStorage.getItem('current-user') ?? 'null');
  const employee = JSON.parse(localStorage.getItem('loggeed-in-employee') ?? 'null');
  
  // Also verify that the sid cookie is still present in the browser
  const sidCookie = cookieService.get('sid');
  
  const isLoggedIn = !!currentUser && !!employee && !!sidCookie;

  if (!isLoggedIn) {
    router.navigate(['/auth/login']);
    localStorage.clear();
    sessionStorage.clear();
    cookieService.deleteAll('/');
    return false;
  }

  return true;
};


