import { inject } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  CanActivateChildFn,
  Router,
  RouterStateSnapshot,
} from '@angular/router';
import { CookieService } from 'ngx-cookie-service';

export const isLoggedInGuard: CanActivateChildFn = (
  route: ActivatedRouteSnapshot,
  state: RouterStateSnapshot
) => {
  const router = inject(Router);
  const cookieService = inject(CookieService);
  
  // Check for authentication data in localStorage
  const currentUser = JSON.parse(localStorage.getItem('current-user') ?? 'null');
  const employee = JSON.parse(localStorage.getItem('loggeed-in-employee') ?? 'null');

  console.log('🔒 Auth Guard - currentUser:', currentUser);
  console.log('🔒 Auth Guard - employee:', employee);

  // For cross-origin authentication, we can't rely on cookies being accessible
  // So we only check localStorage data
  const isLoggedIn = !!currentUser && !!employee;

  console.log('🔒 Auth Guard - isLoggedIn:', isLoggedIn);

  if (!isLoggedIn) {
    router.navigate(['/auth/login']);
    localStorage.clear();
    sessionStorage.clear();
    cookieService.deleteAll('/');
    return false;
  }

  return true;
};


