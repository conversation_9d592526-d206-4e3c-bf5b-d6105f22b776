<div class="min-h-[calc(100vh-80px)] bg-white">
    <header class="bg-primary-lightGreen text-white p-6">
        <div class="flex items-center gap-4">
            <div class="w-12 h-12 rounded-full bg-white/20 overflow-hidden flex items-center justify-center">
                <img src="/icons/user.png" alt="Stock" class="w-10 h-10 icon" />
            </div>
            <div>
                <h1 class="text-lg font-medium">{{ loggedInEmployee.employee_name ?? '--'}}</h1>
                <p class="text-sm text-white/90">{{ loggedInEmployee.designation ?? '--'}}</p>
            </div>
        </div>
    </header>
    <main class="p-4">
        <h2 class="text-xl font-semibold mb-6">Settings</h2>
        <div class="mb-8">
            <h3 class="text-base font-medium text-gray-600 mb-2">Store</h3>
            <button (click)="onDefaultStore()"
                class="w-full flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors">
                <span class="text-gray-700">Default store</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="text-gray-400">
                    <path d="m9 18 6-6-6-6" />
                </svg>
            </button>
        </div>

        <div>
            <h3 class="text-base font-medium text-gray-600 mb-2">Actions</h3>
            <button (click)="onLogout()"
                class="w-full text-left p-3 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors">
                Logout
            </button>
        </div>
    </main>
</div>

<lib-page-footer/>
