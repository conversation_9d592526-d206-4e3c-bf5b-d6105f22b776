<div class="fixed inset-0 z-50 flex items-center justify-center">
  <!-- Backdrop -->
  <div class="fixed inset-0 bg-black/100 backdrop-blur-sm transition-opacity"></div>

  <!-- Modal -->
  <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-4 z-10">
    <!-- Header -->
    <div class="flex justify-between items-center p-4">
      <h2 class="text-xl font-semibold">Edit Item</h2>
      <button (click)="close.emit()" class="text-gray-500 hover:text-gray-700 focus:outline-none">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>

    <!-- Content -->
    <form [formGroup]="formGroup" (ngSubmit)="handleSave()" class="p-4">
      <div class="mb-4">
        <label for="item" class="block text-base font-light text-gray-700 mb-1">Item</label>
        <lib-elevar-inputs [control]="itemControl" [inputId]="'item'" [label]="'item'" [disabled]="true">
        </lib-elevar-inputs>
      </div>

      <div class="mb-6">
        <div class="flex justify-between items-center">
          <label for="quantity" class="text-base font-light text-gray-700">Quantity</label>
          <span class="text-xs font-light text-gray-700">KG</span>
        </div>
        <lib-elevar-inputs [control]="quantityControl" [inputId]="'quantity'" [label]="'quantity'" [type]="'number'">
        </lib-elevar-inputs>
        <div *ngIf="quantityControl.hasError('max') && quantityControl.touched" class="error">
        <div id="password-error-1" class="flex items-center gap-2 text-sm font-medium text-red-500">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z" />
          </svg>            
          <span>Quantity cannot exceed available stock ({{ quantityControl.errors?.['max']?.max }}).</span>
        </div>
      </div>
      </div>
      <!-- Footer -->
      <div class="flex justify-between">
        <button class="text-red-500" (click)="onRemove()">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        </button>
        
        <lib-button type="submit" [buttonType]="isSaveDisabled ? 'disabled' : 'default'" class="w-1/3">
          Save
        </lib-button>
      </div>
    </form>
  </div>
</div>