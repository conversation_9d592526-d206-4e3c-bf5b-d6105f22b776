import { inject, Injectable } from '@angular/core';
import { HttpParams } from '@angular/common/http';

import { Observable } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';

import { BaseHttpService, NotificationService } from '@core';
import { InventoryListResponse, StockReconciliationItem, StockReconciliationListResponse, StockReconciliationPurpose, StockReconcilitionEntry } from '@models';
import { formatDate, formatSimpleDate } from '../auth/common/_datefns_';
import { PosProfileService } from '../pos-profile/pos-profile.service';
import { ItemService } from '../items/items.service';


@Injectable({
  providedIn: 'root'
})
export class InventoryService extends BaseHttpService<InventoryListResponse> {
  private readonly _posProfileService = inject(PosProfileService);
  private readonly _notificationService = inject(NotificationService);
  private readonly _itemService = inject(ItemService);
  constructor() {
    super();
    this.initializeService(
      'Bin',
      'https://elevar-develop.frappe.cloud/api/resource'
    );
  }


  getWareHouseInventoryWithPrices(warehouse: string){
    return this.getWarehouseInventory(warehouse).pipe(
      switchMap(inventory => {
        return this._itemService.getItemPrices().pipe(
          map(prices => {
            return inventory.data.map(item => ({
              ...item,
              price: prices.find(price => price.item_code === item.item_code)?.price_list_rate
            }));
          })
        );
      })
    );
  }


  /**
   * Get stock reconciliation records for a specific company
   * @param company The company to get stock reconciliation records for
   * @returns Observable containing the stock reconciliation records
   */
  getStockReconciliations(company: string): Observable<StockReconciliationListResponse> {
    const fields = ["name", "posting_date", "posting_time", "workflow_state", "docstatus"];
    const filters = [["company", "=", company]];

    const params = new HttpParams()
      .set('filters', JSON.stringify(filters))
      .set('fields', JSON.stringify(fields))
      .set('sort_by', 'posting_date desc');

    // Create the URL for Stock Reconciliation endpoint
    const stockReconciliationUrl = this.apiUrl.replace('Bin', 'Stock Reconciliation');

    return this.http.get<StockReconciliationListResponse>(stockReconciliationUrl, {
      headers: this.getHeaders(),
      params,
      withCredentials: true
    }).pipe(
      catchError(this.handleError)
    );
  }

  saveStockReconciliations(selectedItems: StockReconciliationItem[]) {
    const { warehouse, branch, company } = this._posProfileService.posProfile() ?? {};
    const posting_date = formatSimpleDate(new Date());
    const posting_time = formatDate(new Date());

    // Check for missing required fields
    if (!company || !branch) {
      const missingFields = [
        !company && 'Company',
        !branch && 'Branch'
      ].filter(Boolean).join(', ');

      this._notificationService.setError(
        'Error saving stock reconciliation',
        `Required fields missing: ${missingFields}. Kindly contact Administrator.`
      );

      throw new Error(`Required fields missing: ${missingFields}`);
    }

    const items = this.generateItemsArray(selectedItems, warehouse ?? '');

    const entry = {
      data: {
        docstatus: 1,
        company,
        purpose: StockReconciliationPurpose.STOCK_RECONCILIATION,
        posting_date,
        posting_time,
        branch,
        items
      }
    };

    const stockReconciliationUrl = this.apiUrl.replace('Bin', 'Stock Reconciliation');

    return this.http.post<StockReconcilitionEntry>(stockReconciliationUrl, entry, {
      headers: this.getHeaders(),
      withCredentials: true
    }).pipe(
      catchError(this.handleError)
    );
  }

  getStockReconciliationById(id: string) {
    const stockReconciliationUrl = this.apiUrl.replace('Bin', 'Stock Reconciliation') + '/' + id;
    return this.http.get<any>(stockReconciliationUrl, {
      headers: this.getHeaders(),
      withCredentials: true
    }).pipe(
      map(response => response.data),
      catchError(this.handleError)
    );
  }

  private generateItemsArray(items: any[], warehouse: string) {
    return items.map((item: { item_code: string; quantity: number }) => ({
      item_code: item.item_code,
      warehouse: warehouse,
      qty: item.quantity
  }));
  }

  /**
   * Get available warehouse stock using the custom Frappe method endpoint
   * @param warehouse The warehouse to get available stock for
   */
  getWarehouseInventory(warehouse: string): Observable<InventoryListResponse> {
    // Ensure the URL is /api/method/elevar_group.eg_pos.stock.get_available_warehouse_stock
    const url = this.apiUrl.replace(/\/resource\/Bin$/, '/method/elevar_group.eg_pos.stock.get_available_warehouse_stock').replace(/\/api$/, '/api');

    const params = new HttpParams().set('warehouse', warehouse);
    return this.http.get<InventoryListResponse>(url, {
      headers: this.getHeaders(),
      params,
      withCredentials: true
    }).pipe(
      catchError(this.handleError)
    );
  }
}
