<div class="flex flex-col h-[calc(100vh-80px)] bg-gray-50">
  <lib-page-header [header]="header" [showArrowIcon]="true" [isSearchable]="false" />
  <main class="flex-1 overflow-auto p-4 space-y-4 max-h-[calc(100vh-200px)]">
    <form [formGroup]="formGroup" class="mb-6">
      <div class="relative mb-4">
        <label for="item" class="block text-base font-light text-gray-700 mb-1">Item</label>
        <lib-elevar-inputs [placeholder]="'Search Item'" [control]="searchControl" [inputId]="'search'" [label]="'search'"
          [type]="'text'" (focus)="onSearchFocus()">
        </lib-elevar-inputs>

        @if (searchControl.value && (searchResults$ | async); as results) {
        <div class="absolute z-10 w-full bg-gray-200 shadow-lg rounded-md -mt-4 max-h-60 overflow-y-auto">
          @if (results.length > 0) {
            @for (item of results; track item.item_code) {
            <button type="button" class="w-full text-left px-4 py-2 hover:bg-gray-100 cursor-pointer"
              (click)="selectItem(item)" (keydown.enter)="selectItem(item)">
              {{item.item_code | titlecase}}
            </button>
            }
          } @else {
            <div class="flex flex-col items-center justify-center py-6 px-4 text-center text-gray-500">
              <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto mb-2 h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              <span class="font-medium">No items found</span>
              <span class="text-xs">Your search did not match any items.</span>
            </div>
          }
        </div>
        }

        <div class="mb-4">
          <div class="flex justify-between items-center">
            <label for="item" class="text-base font-light text-gray-700">Quantity</label>
            <label for="item" class="text-xs font-light text-gray-700">KG</label>
          </div>
          <lib-elevar-inputs [control]="quantityControl" [inputId]="'quantity'" [label]="'quantity'" [type]="'number'" min="1"/>
          @if (quantityControl.hasError('stockExceeded')) {
            <div id="password-error-1" class="flex items-center gap-2 text-sm font-medium text-red-500">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z" />
              </svg>
              <span>Quantity cannot exceed available stock ({{ availableStock }}).</span>
            </div>
        }
        @if(quantityControl.touched && quantityControl.invalid){
          <div class="flex items-center gap-2 text-sm font-medium text-red-500">
            <div *ngIf="quantityControl.errors?.['min']">
              Quantity must be at least 1.
            </div>
            <div *ngIf="quantityControl.errors?.['required']">
              Quantity is required.
            </div>
          </div>
        }
        </div>
      </div>
      <label for="item" class="block text-base font-light text-gray-700 mb-1">Store</label>
      <lib-elevar-inputs [control]="formGroup.controls.storeName" [inputId]='"storeName"' [label]="'Store/Warehouse'" [type]="'text'" [disabled]=true/>
        <div class="flex gap-[5px] justify-between flex-wrap">
          <lib-button (click)="handleAddItem()" type="submit" class="w-2/5 inline-block mr-[5px]" [buttonType]="addItemButtonType">
            Add Item
          </lib-button>
          <lib-button (click)="addAllItems()" class="w-2/5 inline-block mr-[5px]" [buttonType]="'default'">
            Add All
          </lib-button>
        </div>
    </form>

    <div class="mt-6" *ngIf="items.length > 0">
      <div class="overflow-hidden rounded-lg border bg-white">
        <table class="w-full">
          <thead>
            <tr class="bg-primary-lightGreen text-white">
              <th class="p-3 text-left font-medium">Item Name</th>
              <th class="p-3 text-left font-medium">Quantity</th>
              <th class="p-3 text-left font-medium">Action</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of items" class="border-b last:border-b-0">
              <td class="p-3">{{ item.item_code }}</td>
              <td class="p-3">{{ item.quantity }}</td>
              <td class="p-3 text-center">
                <button
                  type="button"
                  class="inline-flex h-8 w-8 items-center justify-center rounded-full transition-colors hover:bg-gray-100"
                  (click)="startEditing(item)">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                  </svg>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    @if (showEditModal && editingItem) {
      <lib-edit-record
        [item]="editingItem"
        [type]="'material'"
        (save)="handleEditSave($event)"
        (delete)="handleEditDelete($event)"
        (close)="showEditModal = false">
      </lib-edit-record>
    }
  </main>
  <lib-button type="button" class="fixed right-4 bottom-20 rounded-full" [buttonType]="isNextButtonDisabled ? 'disabled' : 'default'"
    (click)="submitStockReconciliation()">
    <div class="flex items-center justify-center">
      <span>Save</span>
    </div>
  </lib-button>
  <lib-page-footer />
</div>
