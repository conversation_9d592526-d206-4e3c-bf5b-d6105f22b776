@if (login$ | async; as data) { }

@if(isLoading) {
<lib-loader></lib-loader>
}

<lib-toast #loginToast position="top-right" [richColors]="true" [duration]="4000" [closeButton]="true"
  (toastDismissed)="onToastDismissed()" />

<div class="h-screen bg-white flex items-center justify-center p-5">
  <div class="w-full max-w-md">
    <!-- Logo -->
    <div class="w-full flex justify-center items-center -mt-8">
      <img src="/icons/logo-2.svg" alt="Logo" class="w-64 h-32" />
    </div>

    <div class="py-5">
      <p class="text-2xl font-bold py-2">WELCOME BACK</p>
      <p class="text-gray-600 text-sm font-light">
        Enter the details below to continue
      </p>
    </div>

    <form class="space-y-4" [formGroup]="formGroup" (ngSubmit)="onSubmit($event)">
      <div class="mb-10">
        <lib-elevar-inputs [control]="emailControl" inputId="email" label="email"
          [type]="isPassword('email') ? 'password' : isSelector('email') ? 'selector' : 'text'">
        </lib-elevar-inputs>

        <lib-elevar-inputs [control]="passwordControl" inputId="password" label="password"
          [type]="isPassword('password') ? 'password' : isSelector('password') ? 'selector' : 'text'">
        </lib-elevar-inputs>
      </div>

      <div class="w-full container">
        <lib-button [buttonType]="formGroup.valid ? 'default' : 'disabled'" class="w-full block"
          type="submit">LOGIN</lib-button>
      </div>
    </form>
  </div>
</div>