import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'dateDifference'
})
export class DateDifferencePipe implements PipeTransform {

  transform(targetDateStr: string, targetTimeStr: string): number {
    const targetDate = new Date(targetDateStr);
    const currentDate = new Date();

    const differenceInMs = currentDate.getTime() - targetDate.getTime();
    const differenceInDays = Math.floor(differenceInMs / (1000 * 60 * 60 * 24));

    if (differenceInDays > 0) {
      return differenceInDays;
    } else if (targetTimeStr) {
      // Combine date and time for full DateTime comparison
      // Ensure time string is trimmed and formatted correctly
      const trimmedTime = targetTimeStr.trim().replace(/^["']|["']$/g, '');
      const targetDateTime = new Date(`${targetDateStr}T${trimmedTime}`);
      const diffInMs = currentDate.getTime() - targetDateTime.getTime();
      const diffInHours = diffInMs / (1000 * 60 * 60);
      return diffInHours;
    }

    return differenceInDays;
  }
}
