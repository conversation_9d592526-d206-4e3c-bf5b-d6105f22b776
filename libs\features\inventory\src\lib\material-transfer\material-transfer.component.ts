import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';

import { SharedModule } from '@elements/ui';
import { MaterialTransferStore } from '@state';

@Component({
  selector: 'lib-material-transfer',
  imports: [CommonModule, SharedModule],
  templateUrl: './material-transfer.component.html',
  styleUrl: './material-transfer.component.scss'
})
export class MaterialTransferComponent {
  private route = inject(Router);
  requestMaterialStore = inject(MaterialTransferStore);

  header = 'Material Transfer';

  navigateToDetails(name: string) {
    this.route.navigate([`/inventory/single-request-material-details/${name}`]);
  }
}
