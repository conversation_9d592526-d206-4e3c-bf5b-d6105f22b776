import { Component, inject, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { SharedModule } from '@elements/ui';
import { NotificationService } from '@core';
import { MaterialCartService, MaterialRequestService } from '@elevar-clients/api';

@Component({
  selector: 'lib-request-form',
  imports: [CommonModule, SharedModule],
  templateUrl: './request-form.component.html',
  styleUrl: './request-form.component.scss'
})
export class RequestFormComponent implements OnInit {

  private readonly router = inject(Router);
  private readonly route =  inject(ActivatedRoute);
  private readonly _notificationService = inject(NotificationService);
  private readonly _materialService = inject(MaterialRequestService);

  readonly cart = inject(MaterialCartService);

  clue!: string;
  isTransfer = false;
  header = 'Request Form';

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      this.clue = params['clue'];
      if(this.clue === 'transfer') {
        this.isTransfer = true;
      }
    });

    // Check if there are no cart items on init
    if (!this.cart.items || this.cart.items().length === 0) {
      this._notificationService.setError('No items in cart', 'Please add items to your cart before making a request.');
      this.router.navigate(['/inventory/material-request']);
    }
  }

  navigatetoMaterialRequest() {
    if(!this.isTransfer){
      this._materialService.createMaterialRequest()
          .subscribe(() => {
            this.cart.clearCart();
            this._notificationService.setSuccess('Request submitted successfully', 'A new request has been submitted');
            this.router.navigate(['/inventory/material-request']);
          })
    }
  }

  get statusText(): string {
    return this.isTransfer ? 'TRANSFERRED' : 'DRAFT';
  }

  get statusClass(): string {
    return this.isTransfer ? 'text-sm text-blue-500 bg-blue-100 p-2 rounded-md' : 'text-sm text-gray-500';
  }
}
