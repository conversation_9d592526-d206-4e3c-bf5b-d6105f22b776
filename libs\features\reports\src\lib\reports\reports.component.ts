import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from '@elements/ui';
import { FormControl, FormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'lib-reports',
  imports: [CommonModule, SharedModule],
  templateUrl: './reports.component.html',
  styleUrl: './reports.component.css',
})
export class ReportsComponent {
  header = 'Reports';

  formGroup = new FormGroup({
    search: new FormControl('', [Validators.required]),
    selector: new FormControl('', [Validators.required]),
  });

  get searchControl(): FormControl {
    return this.formGroup.get('search') as FormControl;
  }
  
  get selectControl(): FormControl {
    return this.formGroup.get('selector') as FormControl;
  }
}
