import { ApplicationConfig, provideZoneChangeDetection, isDevMode } from '@angular/core';
import { provideRouter } from '@angular/router';
import { appRoutes } from './app.routes';
import { provideServiceWorker } from '@angular/service-worker';
import { provideHttpClient, withInterceptors } from '@angular/common/http';
import { cookieInterceptor, errorInterceptor } from '@core';
import { initializeApp, provideFirebaseApp } from '@angular/fire/app';
import { getAuth, provideAuth } from '@angular/fire/auth';
import { getFirestore, provideFirestore } from '@angular/fire/firestore';
import { getFunctions, provideFunctions } from '@angular/fire/functions';
import { getMessaging, provideMessaging } from '@angular/fire/messaging';
import { getStorage, provideStorage } from '@angular/fire/storage';


export const appConfig: ApplicationConfig = {
  providers: [
    provideHttpClient(withInterceptors([cookieInterceptor, errorInterceptor])),
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(appRoutes), provideServiceWorker('ngsw-worker.js', {
      enabled: !isDevMode(),
      registrationStrategy: 'registerWhenStable:30000'
    }), provideFirebaseApp(() => initializeApp({ projectId: "elevar-a5347", appId: "1:670563079940:web:0b8c12cb1f56349c4ae251", storageBucket: "elevar-a5347.firebasestorage.app", apiKey: "AIzaSyCwdtUyYBVXQ7srUWC4baStfAE_tiNEafo", authDomain: "elevar-a5347.firebaseapp.com", messagingSenderId: "670563079940", measurementId: "G-2ZM39PJ3BY" })), provideAuth(() => getAuth()), provideFirestore(() => getFirestore()), provideFirebaseApp(() => initializeApp({ projectId: "elevar-a5347", appId: "1:670563079940:web:0b8c12cb1f56349c4ae251", storageBucket: "elevar-a5347.firebasestorage.app", apiKey: "AIzaSyCwdtUyYBVXQ7srUWC4baStfAE_tiNEafo", authDomain: "elevar-a5347.firebaseapp.com", messagingSenderId: "670563079940", measurementId: "G-2ZM39PJ3BY" })), provideAuth(() => getAuth()), provideFirestore(() => getFirestore()), provideFunctions(() => getFunctions()), provideMessaging(() => getMessaging()), provideStorage(() => getStorage())
  ],
};
