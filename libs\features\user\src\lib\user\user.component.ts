import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from '@elements/ui';
import { AuthService } from '@elevar-clients/api';

@Component({
  selector: 'lib-user',
  imports: [CommonModule, SharedModule],
  templateUrl: './user.component.html',
  styleUrl: './user.component.css',
})
export class UserComponent {
  header = 'User';

  loggedInEmployee = JSON.parse(localStorage.getItem('loggeed-in-employee') ?? 'null')
  private authService = inject(AuthService);

  onLogout(): void {
    this.authService.logout()
       .subscribe((res) => {
        console.log('I AM LOGGING OUT')
       })
  }

  onDefaultStore(): void {
    console.log('Default store clicked');
  }
}
