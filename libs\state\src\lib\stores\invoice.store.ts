import {inject} from '@angular/core';

import {patchState, signalStore, type, withHooks} from '@ngrx/signals';

import { lastValueFrom } from 'rxjs';

import { POSInvoice } from '@models';
import { setAllEntities, withEntities } from '@ngrx/signals/entities';
import { withCalls, withCallStatus, withEntitiesLocalFilter } from '@ngrx-traits/signals';
import { InvoiceService } from '@elevar-clients/api';

/* REF:
  https://www.stefanos-lignos.dev/posts/ngrx-signals-store
  https://www.youtube.com/watch?v=HqxY0JPlh54
  https://medium.com/@gabrieldavidguerrero/introducing-ngrx-traits-signals-1b3ff9af67ec
*/

type InvoiceStore = {
    invoices: POSInvoice[];
    filter: string,
    error: Error | null;
    loading: boolean
  };

const entity = type<POSInvoice>();
export const InvoiceStore = signalStore(
  {providedIn: 'root'},

  withEntities({ entity }),
  withCallStatus({ initialValue: 'loading' }),
  withEntitiesLocalFilter({
    entity,
    defaultFilter: {
      search: '',
    },
    filterFn: (entity, filter) =>
      !filter?.search ||
      Object.values(entity).some(value =>
      typeof value === 'string' && value.toLowerCase().includes(filter.search.toLowerCase())
    ),
  }),

  withHooks(({ setLoaded, setError, ...store }) => ({
    onInit: async () => {
      const invoiceService = inject(InvoiceService);
      try {
        const invoices = await lastValueFrom(invoiceService.getAllInvoices());
        const invoicesWithIds = invoices.map((invoice, index) => ({
          ...invoice,
          id: index + 1,
        }));
        patchState(store, setAllEntities(invoicesWithIds));
        setLoaded();
      } catch (e) {
        setError(e);
      }
    },
  })),
  withCalls(({ setLoaded, setError, ...store })=> ({
    // This method here just orders the invoices by ascending or descending order using the posting date property.
    // Hence the new param `order` is added to the method signature (getAllInvoices()) 😬.
    // It is not being used anywhere in the codebase or rather at component level.
    // Maybe it was intended to be used in the future.
    loadByOrder: async ({ order }: { order: 'asc' | 'desc' | undefined }) => {
      try {
      const invoices = await lastValueFrom(inject(InvoiceService).getAllInvoices(order));
      const invoicesWithIds = invoices.map((invoice, index) => ({
        ...invoice,
        id: index + 1,
      }));
      patchState(store, setAllEntities(invoicesWithIds));
      setLoaded();
    } catch (e) {
      setError(e);
    }
  }
})),
)

