@if (invoices$ | async) {}
@if (trackOpeningEntry$ | async) {}

@if (!invoiceStore.isLoading()) {
<div class="flex flex-col h-[calc(100vh-80px)] bg-gray-50">
  <lib-page-header [header]="header" [showArrowIcon]="false" [isSearchable]="true" [placeholder]="'Search Invoice'" (search)="onSearch($event)"/>

  @if(hasOpeningEntryToday()){
  <div class="flex items-center justify-between p-4 border-b border-gray-200">
    <div class="flex items-center space-x-2">
      <div class="h-2 w-2 bg-green-500 rounded-full animate-blink"></div>
      <span class="text-sm font-medium text-gray-700">POS is open</span>
    </div>
    <button (click)="navigateToCloseEntries()"
      class="text-blue-600 hover:text-blue-800 text-sm font-medium focus:outline-none">
      Close
    </button>
  </div>
  }
  <main class="flex-1 overflow-auto p-4 space-y-4 max-h-[calc(100vh-255px)]">
    @for (invoice of invoiceStore.entities(); track invoice.name) {
    <lib-product-card  [document]="invoice" [type]="'invoice'" (click)="navigateToInvoiceDetail(invoice.name)"></lib-product-card>
    }
  </main>
  <lib-button type="submit" class="fixed right-4 bottom-20 rounded-full" (click)="navigateToInvoicePage()">
    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2">
      <line x1="12" y1="5" x2="12" y2="19"></line>
      <line x1="5" y1="12" x2="19" y2="12"></line>
    </svg>
    <span>New Invoice</span>
  </lib-button>

  <lib-page-footer />
</div>
}
