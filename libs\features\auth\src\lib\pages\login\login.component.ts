import { Component, inject, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { catchError, EMPTY, Observable, tap } from 'rxjs';
import { ButtonComponent, ElevarInputsComponent, LoaderComponent, ToastComponent } from '@elements/ui';
import { LoginRequest } from '@models';
import { AuthService } from '@elevar-clients/api';

@Component({
  selector: 'auth-login',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ElevarInputsComponent,
    ButtonComponent,
    LoaderComponent,
    ToastComponent
  ],
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss',
})
export class LoginComponent {
  @ViewChild('loginToast') toastComponent!: ToastComponent;

  private readonly _authService = inject(AuthService);
  private readonly _router = inject(Router);

  login$ = new Observable<unknown>();
  showPassword = false;
  isLoading = false;

  formGroup = new FormGroup({
    email: new FormControl('', [Validators.required, Validators.email]),
    password: new FormControl('', [Validators.required])
  });

  togglePasswordVisibility(): void {
    this.showPassword = !this.showPassword;
  }

  get formIsValid(): boolean {
    return this.formGroup.valid;
  }

  onSubmit(event: Event): void {
    event.preventDefault();
    console.log('🔥 Login form submitted');

    if (!this.formIsValid) {
      this.toastComponent.error('Invalid Form', {
        description: 'Please check your email and password',
        duration: 4000
      });
      return;
    }

    this.isLoading = true;

    const credentials: LoginRequest = {
      usr: this.formGroup.get('email')?.value as string,
      pwd: this.formGroup.get('password')?.value as string
    };

    console.log('🔥 Calling auth service login with:', credentials);

    this.login$ = this._authService.login(credentials).pipe(
      tap((res) => {
        console.log('🔥 Login component received result:', res);
        this.isLoading = false;
        if (res) {
          console.log('🔥 Login successful, navigating to invoices');
          this.toastComponent.success('✅Login Successful', {
            description: 'Welcome back!',
            duration: 3000
          });

          this._router.navigate(['/invoices']);
        } else {
          console.log('🔥 Login failed - no employee found');
          this.toastComponent.error('Login Failed', {
            description: '⛔Cannot Find Employee.',
            duration: 5000
          });
        }
      }),
      catchError(err => {
        console.log(err);
        this.isLoading = false;
        this.toastComponent.error('Login Failed', {
          description: this.getErrorMessage(err),
          duration: 5000
        });
        return EMPTY;
      })
    );
  }

  private getErrorMessage(error: any): string {
    if (error?.status === 401) {
      return 'Invalid email or password';
    } else if (error?.status === 429) {
      return 'Too many attempts. Please try again later';
    }
    return 'An unexpected error occurred. Please try again';
  }

  isSelector(formControl: string) {
    return formControl.includes('selected');
  }

  isPassword(formControl: string) {
    return formControl.includes('password');
  }

  get emailControl(): FormControl {
    return this.formGroup.get('email') as FormControl;
  }

  get passwordControl(): FormControl {
    return this.formGroup.get('password') as FormControl;
  }

  onToastDismissed() {
    console.log('Toast dismissed');
  }
}