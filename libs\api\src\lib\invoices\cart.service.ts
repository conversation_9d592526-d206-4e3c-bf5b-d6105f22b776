import { Injectable, computed, effect, inject, signal } from '@angular/core';
import { NotificationService } from '@core';
import { CartItem, Item } from '@models';

@Injectable({
  providedIn: 'root'
})
export class CartService {
  private readonly STORAGE_KEY = 'cart_items';

  private readonly _notificationService = inject(NotificationService)
  
  private readonly cartItems = signal<CartItem[]>(this.loadCartFromStorage());
  
  // Computed signals for derived state
  readonly items = computed(() => this.cartItems());
  readonly totalItems = computed(() => this.cartItems().length);
  readonly isEmpty = computed(() => this.totalItems() === 0);
  
  constructor() {
    // Effect for persisting to localStorage whenever cart changes
    effect(() => {
      const currentItems = this.cartItems();
      this.saveCartToStorage(currentItems);
    });
  }

  /**
   * Add or update item in cart
   */
  addItem(item: Item, quantity: number): void {
    const currentItems = this.cartItems();
    const existingItemIndex = currentItems.findIndex(
      cartItem => cartItem.item_code === item.item_code
    );

    if (existingItemIndex !== -1) {
      // Update existing item
      this.cartItems.update(items => items.map((cartItem, index) => 
        index === existingItemIndex
          ? { ...cartItem, quantity }
          : cartItem
      ));
    } else {
      // Add new item
      const newCartItem: CartItem = {
        ...item,
        quantity,
        isEditing: false
      };
      this.cartItems.update(items => [...items, newCartItem]);
    }

    this._notificationService.setSuccess('Item added', `Item ${item.item_code} added to the invoice`)
  }

  /**
   * Update an existing cart item
   */
  updateItem(updatedItem: CartItem): void {
    this.cartItems.update(items => 
      items.map(item => 
        item.item_code === updatedItem.item_code
          ? { ...updatedItem, isEditing: false, editQuantity: undefined }
          : item
      )
    );

    this._notificationService.setSuccess('Item updated', `Item: ${updatedItem.item_code} updated in the invoice`)
  }

  updateCart(items: CartItem[]) {
    this.cartItems.set(items);
  }

  /**
   * Remove an item from cart
   */
  removeItem(itemCode: string): void {
    this.cartItems.update(items => 
      items.filter(item => item.item_code !== itemCode)
    );

    this._notificationService.setSuccess('Item removed', `Item: ${itemCode} removed from the invoice`)
  }

  /**
   * Clear all items from cart
   */
  clearCart(): void {
    this.cartItems.set([]);
    this._notificationService.setWarning('Invoice cleared', 'Items have been removed from the invoice')
  }

  /**
   * Start editing an item
   */
  startEditing(itemCode: string): void {
    this.cartItems.update(items =>
      items.map(item => ({
        ...item,
        isEditing: item.item_code === itemCode,
        editQuantity: item.item_code === itemCode ? item.quantity : undefined
      }))
    );
  }

  /**
   * Cancel editing for an item
   */
  cancelEditing(itemCode: string): void {
    this.cartItems.update(items =>
      items.map(item => ({
        ...item,
        isEditing: false,
        editQuantity: undefined
      }))
    );
  }

  /**
   * Update edit quantity for an item
   */
  updateEditQuantity(itemCode: string, quantity: number): void {
    if (quantity <= 0) return;
    
    this.cartItems.update(items =>
      items.map(item =>
        item.item_code === itemCode
          ? { ...item, editQuantity: quantity }
          : item
      )
    );
  }

  /**
   * Save edited quantity for an item
   */
  saveEditing(itemCode: string): void {
    this.cartItems.update(items =>
      items.map(item => {
        if (item.item_code === itemCode && item.editQuantity && item.editQuantity > 0) {
          return {
            ...item,
            quantity: item.editQuantity,
            isEditing: false,
            editQuantity: undefined
          };
        }
        return item;
      })
    );

    this._notificationService.setSuccess('Item updated', `Item ${itemCode} updated in the invoice`)
  }

  /**
   * Get a specific item from cart
   */
  getItem(itemCode: string): CartItem | undefined {
    return this.cartItems().find(item => item.item_code === itemCode);
  }

  /**
   * Check if an item exists in cart
   */
  hasItem(itemCode: string): boolean {
    return this.cartItems().some(item => item.item_code === itemCode);
  }

  /**
   * Load cart from localStorage
   */
  private loadCartFromStorage(): CartItem[] {
    try {
      const storedCart = localStorage.getItem(this.STORAGE_KEY);
      return storedCart ? JSON.parse(storedCart) : [];
    } catch (error) {
      console.error('Error loading cart from localStorage:', error);
      return [];
    }
  }

  /**
   * Save cart to localStorage
   */
  private saveCartToStorage(items: CartItem[]): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(items));
    } catch (error) {
      console.error('Error saving cart to localStorage:', error);
    }
  }
}