<div class="flex flex-col h-[calc(100vh-80px)] bg-gray-50">
  <lib-page-header [header]="'Inventory Management'" [showArrowIcon]="false" [isSearchable]="false" />
  <main class="flex-1 flex items-center justify-center p-6 pt-0 max-h-[calc(100vh-255px)]">
    <div class="grid grid-cols-2 gap-4 max-w-lg w-full">
      @for(item of menuItems; track item.title){
      <button
        class="bg-primary-lightGreen hover:bg-primary-hoverGreen text-white h-32 flex flex-col items-center justify-center space-y-2 rounded-lg transition-colors duration-200"
        (click)="item.action()">
        <span [innerHTML]="item.icon"></span>
        <span class="font-medium">{{item.title}}</span>
      </button>
      }
    </div>
  </main>
  <lib-page-footer />
</div>
