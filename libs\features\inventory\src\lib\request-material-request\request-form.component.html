<lib-page-header [header]="header" [showArrowIcon]="true" />

<main class="flex-grow p-4 bg-gray-50 min-h-screen">
  <div class="max-w-xl mx-auto bg-white rounded-2xl shadow-lg p-6 space-y-8">
    <div class="flex justify-end items-center mb-4">
      <span [ngClass]="statusClass" class="px-3 py-1  font-semibold text-sm text-blue-500 bg-blue-100 p-2 rounded-md">{{ statusText }}</span>

    </div>

    <div class="space-y-4">
      @for (item of cart.items(); track item.item_code) {
        <div class="bg-gray-100 rounded-lg p-4 flex justify-between items-center shadow-sm">
          <div>
            <h2 class="font-semibold text-gray-800">{{item.item_code}}</h2>
            <p class="text-lg text-gray-600">{{item.quantity}} <span class="font-medium text-xs">KG</span></p>
          </div>
        </div>
      }
    </div>

    @if(!isTransfer){
      <div class="flex justify-center items-center pt-4">
        <lib-button type="submit" class="w-full" (click)="navigatetoMaterialRequest()">
          REQUEST
        </lib-button>
      </div>
    }

    @if(isTransfer){
      <div class="flex justify-between w-full gap-4 pt-4">
        <button class="flex-1 bg-indigo-100 text-indigo-700 px-4 py-2 rounded-lg font-medium hover:bg-indigo-200 transition">Action 1</button>
        <button class="flex-1 bg-green-100 text-green-700 px-4 py-2 rounded-lg font-medium hover:bg-green-200 transition">Action 2</button>
      </div>
    }
  </div>
</main>

<lib-page-footer />
