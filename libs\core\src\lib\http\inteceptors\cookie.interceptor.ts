import { HttpInterceptorFn, HttpErrorResponse } from '@angular/common/http';
import { catchError } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { inject, isDevMode } from '@angular/core';
import { CookieService } from 'ngx-cookie-service';
import { Router } from '@angular/router';

export const cookieInterceptor: HttpInterceptorFn = (req, next) => {
  const cookieService = inject(CookieService);
  const router = inject(Router);

  return next(req.clone({
    withCredentials: true
  })).pipe(
    catchError((error: HttpErrorResponse) => {
      if (isDevMode()) {
        console.error('HTTP Error:', error);
      }

      // Handle 403 Forbidden - session expired or invalid
      if (error.status === 403) {
        // Clear all auth data
        localStorage.clear();
        sessionStorage.clear();

        // Clear ERPNext cookies
        const cookieNames = ['sid', 'user_id', 'full_name', 'system_user', 'user_image'];
        cookieNames.forEach(name => {
          cookieService.delete(name, '/');
          cookieService.delete(name);
        });

        router.navigate(['/auth/login']);
      }

      return throwError(() => error);
    })
  );
};
