import { HttpInterceptorFn, HttpResponse, HttpErrorResponse } from '@angular/common/http';
import { catchError, tap } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { inject, isDevMode } from '@angular/core';
import { CookieService } from 'ngx-cookie-service';
import { Router } from '@angular/router';
import { LoginResponse } from '@models';

export const cookieInterceptor: HttpInterceptorFn = (req, next) => {
  const cookieService = inject(CookieService);
  const router = inject(Router);
  
  return next(req.clone({
    withCredentials: true
  })).pipe(
    tap(event => {
      if (event instanceof HttpResponse) {
        if (isDevMode()) {
          console.log('HTTP Response intercepted:', {
            url: event.url,
            body: event.body,
            status: event.status
          });
        }

        // Check if this is a login response (handle both proxied and direct URLs)
        const isLoginUrl = event.url?.includes('/api/method/login') || event.url?.endsWith('/login');
        if (isLoginUrl) {
          if (isDevMode()) {
            console.log('Login URL detected, checking response body:', event.body);
          }

          if ((event.body as LoginResponse)?.message === 'Logged In') {
            if (isDevMode()) {
              console.log('Login successful response detected');
            }
          // Use setTimeout to ensure cookies are set by the browser before reading them
          setTimeout(() => {
            // Extract ERPNext cookies and store them in a structured way
            const cookies = cookieService.getAll();

            if (isDevMode()) {
              console.log('All cookies after login:', cookies);
            }

            // Filter out Guest cookies and get the actual user cookies
            const validCookies = Object.keys(cookies).reduce((acc, key) => {
              const value = cookies[key];
              // Skip Guest cookies and empty values
              if (value && value !== 'Guest' && value.trim() !== '') {
                acc[key] = value;
              }
              return acc;
            }, {} as Record<string, string>);

            if (isDevMode()) {
              console.log('Valid cookies after filtering:', validCookies);
            }

            // Only store if we have the essential ERPNext cookies with valid values
            if (validCookies['sid'] && validCookies['user_id'] && validCookies['user_id'] !== 'Guest') {
              const erpnextUser = {
                user_id: decodeURIComponent(validCookies['user_id']),
                full_name: validCookies['full_name'] ? decodeURIComponent(validCookies['full_name']) : '',
                sid: validCookies['sid'],
                system_user: validCookies['system_user'] || 'no',
                user_image: validCookies['user_image'] || ''
              };

              if (isDevMode()) {
                console.log('Storing user in localStorage:', erpnextUser);
              }

              // Store in localStorage for persistence
              localStorage.setItem('current-user', JSON.stringify(erpnextUser));
            } else {
              if (isDevMode()) {
                console.error('Missing essential cookies for user storage:', {
                  sid: validCookies['sid'],
                  user_id: validCookies['user_id']
                });
              }
            }
          }, 100); // Small delay to ensure cookies are set
          } else {
            if (isDevMode()) {
              console.log('Login response but message is not "Logged In":', (event.body as LoginResponse)?.message);
            }
          }
        } else {
          if (isDevMode() && event.url?.includes('login')) {
            console.log('Login-related URL but not /api/method/login:', event.url);
          }
        }
      }
    }),
    catchError((error: HttpErrorResponse) => {
      if (isDevMode()) {
        console.error('Interceptor Error:', error);
      }

      if (error.status === 403) {
        localStorage.clear();
        cookieService.deleteAll('/');
        router.navigate(['/auth/login']);
      }
      
      return throwError(() => error);
    })
  );
};
