import { HttpInterceptorFn, HttpErrorResponse } from '@angular/common/http';
import { catchError } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { inject, isDevMode } from '@angular/core';
import { CookieService } from 'ngx-cookie-service';
import { Router } from '@angular/router';

export const cookieInterceptor: HttpInterceptorFn = (req, next) => {
  const cookieService = inject(CookieService);
  const router = inject(Router);

  return next(req.clone({
    withCredentials: true
  })).pipe(
    catchError((error: HttpErrorResponse) => {
      if (isDevMode()) {
        console.error('HTTP Error:', error);
      }

      // Handle 403 Forbidden - but be selective about clearing auth data
      if (error.status === 403) {
        console.log('🚨 403 Forbidden error for URL:', error.url);

        // Only clear auth data for login/auth related endpoints
        // Don't clear for other API endpoints that might fail due to cross-origin issues
        if (error.url?.includes('/api/method/login') || error.url?.includes('/auth/')) {
          console.log('🚨 Auth-related 403, clearing auth data');
          localStorage.clear();
          sessionStorage.clear();

          // Clear ERPNext cookies
          const cookieNames = ['sid', 'user_id', 'full_name', 'system_user', 'user_image'];
          cookieNames.forEach(name => {
            cookieService.delete(name, '/');
            cookieService.delete(name);
          });

          router.navigate(['/auth/login']);
        } else {
          console.log('🚨 Non-auth 403 error, not clearing auth data');
        }
      }

      return throwError(() => error);
    })
  );
};
