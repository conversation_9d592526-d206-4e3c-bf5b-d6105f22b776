import { HttpInterceptorFn, HttpResponse, HttpErrorResponse } from '@angular/common/http';
import { catchError, tap } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { inject, isDevMode } from '@angular/core';
import { CookieService } from 'ngx-cookie-service';
import { Router } from '@angular/router';
import { LoginResponse } from '@models';

export const cookieInterceptor: HttpInterceptorFn = (req, next) => {
  const cookieService = inject(CookieService);
  const router = inject(Router);
  
  return next(req.clone({
    withCredentials: true
  })).pipe(
    tap(event => {
      if (event instanceof HttpResponse) {
        // Check if this is a login response
        if (event.url?.includes('/api/method/login') && (event.body as LoginResponse)?.message === 'Logged In') {
          // Extract ERPNext cookies and store them in a structured way
          const cookies = cookieService.getAll();
          
          // Only store if we have the essential ERPNext cookies
          if (cookies['sid'] && cookies['user_id']) {
            const erpnextUser = {
              user_id: decodeURIComponent(cookies['user_id']),
              full_name: cookies['full_name'] ? decodeURIComponent(cookies['full_name']) : '',
              sid: cookies['sid'],
              system_user: cookies['system_user'] || 'no',
              user_image: cookies['user_image'] || ''
            };
            
            // Store in localStorage for persistence
            localStorage.setItem('current-user', JSON.stringify(erpnextUser));
          }
        }
      }
    }),
    catchError((error: HttpErrorResponse) => {
      if (isDevMode()) {
        console.error('Interceptor Error:', error);
      }

      if (error.status === 403) {
        localStorage.clear();
        cookieService.deleteAll('/');
        router.navigate(['/auth/login']);
      }
      
      return throwError(() => error);
    })
  );
};
