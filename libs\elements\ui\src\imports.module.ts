import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ButtonComponent, ElevarInputsComponent, PageFooterComponent, PageHeaderComponent, ProductCardComponent, SearchInputComponent } from '.';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ButtonComponent,
    PageHeaderComponent,
    PageFooterComponent,
    SearchInputComponent,
    ElevarInputsComponent,
    ProductCardComponent
  ],
  exports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ButtonComponent,
    PageHeaderComponent,
    <PERSON>FooterComponent,
    SearchInputComponent,
    ElevarInputsComponent,
    ProductCardComponent
  ]
})
export class SharedModule {}
