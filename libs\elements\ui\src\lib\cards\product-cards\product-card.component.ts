import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

import { POSInvoice } from '@models';
import { DateDifferencePipe } from '@core';

@Component({
  selector: 'lib-product-card',
  imports: [CommonModule, DateDifferencePipe],
  templateUrl: './product-card.component.html',
})
export class ProductCardComponent {

  @Input() document!: POSInvoice | any; //TODO: Remove any
  @Input() type!: string;
  @Input() showLocation = false;
  @Output() navigateToInvoiceDetail = new EventEmitter<string>();

  getStatusColor = (status: string | undefined | null) => {
    if (!status) {
      return 'text-gray-500 bg-gray-100';
    }
    switch (status.toLowerCase()) {
      case 'return':
        return 'text-red-500 bg-red-100';
      case 'paid':
        return 'text-green-500 bg-green-100';
      case 'new invoice':
        return 'text-blue-500 bg-blue-100';
      default:
        return 'text-gray-500 bg-gray-100';
    }
  }


}
