import { Component, EventEmitter, Input, OnInit, Output, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ButtonComponent, TemplateDrivenInputComponent } from '@elements/ui';
import { ModeOfPayment, POSInvoicePayment } from '@models';
import { ModeOfPaymentsService } from '@elevar-clients/api';


@Component({
  selector: 'lib-payment-modal',
  standalone: true,
  imports: [CommonModule, FormsModule, ButtonComponent, TemplateDrivenInputComponent],
  templateUrl: './payment-modal.component.html',
  styleUrls: ['./payment-modal.component.scss']
})
export class PaymentModalComponent implements OnInit {
  @Input() amount = 0;
  @Input() posInvoicePayments: POSInvoicePayment[] = [];
  @Output() close = new EventEmitter<void>();
  @Output() save = new EventEmitter<POSInvoicePayment[]>();
  @Input() submitInvoice: (() => void) | undefined;

  paymentOptions: { value: string, label: string }[] = [];
  private readonly modeOfPaymentsService = inject(ModeOfPaymentsService);

  payments: { modeOfPayment: ModeOfPayment; amount: number; isOpen: boolean; isEditing: boolean }[] = [];

  ngOnInit() {
    this.modeOfPaymentsService.getAllEnabledModesOfPayment().subscribe((modes: { name: string }[]) => {
      this.paymentOptions = modes.map((m: { name: string }) => ({ value: m.name, label: m.name }));
    });
    if (this.posInvoicePayments && this.posInvoicePayments.length > 0) {
      this.payments = this.posInvoicePayments.map(payment => ({
        modeOfPayment: payment.mode_of_payment as ModeOfPayment,
        amount: payment.amount,
        isOpen: false,
        isEditing: false
      }));
    } else {
      this.addPayment();
    }
  }

  addPayment() {
    this.payments.push({ modeOfPayment: ModeOfPayment.CASH, amount: 0, isOpen: true, isEditing: true });
  }

  removePayment(index: number) {
    this.payments.splice(index, 1);
  }

  toggleAccordion(index: number) {
    this.payments[index].isOpen = !this.payments[index].isOpen;
  }

  toggleEdit(index: number) {
    const payment = this.payments[index];
    payment.isEditing = !payment.isEditing;
    if (!payment.isEditing) {
      payment.isOpen = false;
    }
  }

  get isAnyPaymentEditing(): boolean {
    return this.payments.some(payment => payment.isEditing);
  }

  get totalPaid(): number {
    return this.payments.reduce((sum, payment) => sum + (payment.amount || 0), 0);
  }

  get change(): number {
    const hasCash = this.payments.some(payment => payment.modeOfPayment === 'cash');
    return hasCash && this.totalPaid > this.amount ? this.totalPaid - this.amount : 0;
  }

  get canProceed(): boolean {
    // Check if array is empty, any payment lacks a modeOfPayment, or any payment is being edited
    if (this.payments.length === 0 ||
        this.payments.some(payment => !payment.modeOfPayment) ||
        this.isAnyPaymentEditing) {
      return false;
    }

    // Check if total paid is less than the amount
    if (this.totalPaid < this.amount) {
      return false;
    }
    const allNonCash = this.payments.every(payment => payment.modeOfPayment !== 'cash');

    if (allNonCash) {
      return this.totalPaid === this.amount;
    }

    return this.totalPaid >= this.amount;
  }

  onProceed() {
    if (!this.canProceed) return;

    const payments: POSInvoicePayment[] = this.payments.map(payment => ({
      mode_of_payment: payment.modeOfPayment,
      amount: payment.amount
    }));

    // If remaining amount is zero, call submitInvoice if provided
    if (this.totalPaid === this.amount && this.submitInvoice) {
      this.save.emit(payments); // still emit payments for parent state
      this.submitInvoice();
      return;
    }
    this.save.emit(payments);
  }

  trackByIndex(index: number): number {
    return index;
  }
}
