import { inject, Injectable, isDevMode } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpHeaders, HttpParams, HttpResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

@Injectable()
export class BaseHttpService<T> {
  protected http = inject(HttpClient);
  protected apiUrl = '';

  protected initializeService(endpoint: string, baseUrl: string) {
    // Check if we're running on localhost (development with proxy) or deployed
    const isLocalhost = typeof window !== 'undefined' &&
                       (window.location.hostname === 'localhost' ||
                        window.location.hostname === '127.0.0.1');

    if (isDevMode() && isLocalhost) {
      // Development with proxy - use relative URLs
      const urlPath = baseUrl.replace(/^https?:\/\/[^/]+/, '');
      this.apiUrl = `${urlPath}/${endpoint}`.replace(/\/+$/, '');
    } else {
      // Production or development without proxy - use full URLs
      this.apiUrl = `${baseUrl}/${endpoint}`.replace(/\/+$/, '');
    }
  }

  /**
   * Configure request headers
   */
  protected getHeaders(): HttpHeaders {
    return new HttpHeaders({
      'Content-Type': 'application/json'
    });
  }

  /**
   * Get all records
   */
  getAll(params?: HttpParams): Observable<T[]> {
    return this.http
      .get<T[]>(this.apiUrl, { 
        headers: this.getHeaders(), 
        params,
        withCredentials: true 
      })
      .pipe(catchError(this.handleError));
  }

  /**
   * Get a single record by ID
   */
  getById(id: string | number): Observable<T> {
    const url = `${this.apiUrl}/${id}`;
    return this.http
      .get<T>(url, { 
        headers: this.getHeaders(),
        withCredentials: true 
      })
      .pipe(catchError(this.handleError));
  }

  /**
   * Create a new record
   */
  create(item: Partial<T>): Observable<T> {
    return this.http
      .post<T>(
        this.apiUrl, 
        item, 
        { 
          headers: this.getHeaders(),
          withCredentials: true 
        }
      )
      .pipe(catchError(this.handleError));
  }

  /**
   * Update an existing record
   */
  update(id: string | number, item: Partial<T>): Observable<T> {
    const url = `${this.apiUrl}/${id}`;
    return this.http
      .put<T>(
        url, 
        item, 
        { 
          headers: this.getHeaders(),
          withCredentials: true 
        }
      )
      .pipe(catchError(this.handleError));
  }

  /**
   * Patch an existing record
   */
  patch(id: string | number, item: Partial<T>): Observable<T> {
    const url = `${this.apiUrl}/${id}`;
    return this.http
      .patch<T>(
        url, 
        item, 
        { 
          headers: this.getHeaders(),
          withCredentials: true 
        }
      )
      .pipe(catchError(this.handleError));
  }

  /**
   * Delete a record
   */
  delete(id: string | number): Observable<void> {
    const url = `${this.apiUrl}/${id}`;
    return this.http
      .delete<void>(
        url, 
        { 
          headers: this.getHeaders(),
          withCredentials: true 
        }
      )
      .pipe(catchError(this.handleError));
  }

  /**
   * Post with full response
   */
  postWithFullResponse(data: any): Observable<HttpResponse<T>> {
    return this.http
      .post<T>(
        this.apiUrl, 
        data, 
        {
          headers: this.getHeaders(),
          withCredentials: true,
          observe: 'response'
        }
      )
      .pipe(catchError(this.handleError));
  }

  /**
   * Handle HTTP errors
   */
  protected handleError(error: HttpErrorResponse) {
    let errorMessage = 'An error occurred';

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Error: ${error.error.message}`;
    } else {
      // Server-side error
      errorMessage = `Error Code: ${error.status}\nMessage: ${error.message}`;
    }

    console.error(errorMessage);
    return throwError(() => new Error(errorMessage));
  }
}