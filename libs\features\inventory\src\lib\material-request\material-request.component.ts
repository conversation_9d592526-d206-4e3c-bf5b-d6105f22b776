import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';

import { SharedModule } from '@elements/ui';

import { MaterialRequestStore } from '@state';
import { MaterialTransferService } from '@elevar-clients/api';

@Component({
  selector: 'lib-material-request',
  imports: [CommonModule, SharedModule],
  templateUrl: './material-request.component.html',
  styleUrl: './material-request.component.scss'
})
export class MaterialRequestComponent {
  materialTransferService = inject(MaterialTransferService);
  private readonly router = inject(Router);
  store = inject(MaterialRequestStore);

  header = 'Material Request';

  // Todo: This page is reused in two areas, at this point we need to pass in the details
  // on the query params to be displayed on the request-material page.
  navigateToNewRequest(){
    this.router.navigate(['/inventory/new-material-request']);
  }

  navigatetoMaterialRequestDetails(name: string) {
    this.router.navigateByUrl(`/inventory/request-details/${name}`);
  }
}
