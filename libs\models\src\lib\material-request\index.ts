export interface MaterialRequestEntry {
    data: {
      workflow_state: string,
      schedule_date: string,
      material_request_type: string,
      company: string;
      set_warehouse: string,
      transaction_date: string,
      items: MaterialItem[]
    }
  }

export interface MaterialItem {
    item_code: string,
    qty: number | string
}

export interface MaterialRequest {
  name: string;
  transaction_date: string;
  material_request_type: string;
  status: string;
}

export enum WorkflowState {
  DRAFT = 'Draft',
}

export enum MaterialRequestType {
  PURCHASE = 'Purchase',
  MATERIAL_TRANSFER = 'Material Transfer'
}

export interface MaterialRequestResponseItem {
  name: string;
  owner: string;
  creation: string;
  modified: string;
  modified_by: string;
  docstatus: number;
  idx: number;
  item_code: string;
  item_name: string;
  schedule_date: string;
  description: string;
  item_group: string;
  brand: string | null;
  image: string;
  qty: number;
  stock_uom: string;
  warehouse: string;
  uom: string;
  conversion_factor: number;
  stock_qty: number;
  projected_qty: number;
  actual_qty: number;
  ordered_qty: number;
  received_qty: number;
  rate: number;
  amount: number;
  expense_account: string;
  cost_center: string;
  doctype: string;
}

export interface MaterialRequestDetailResponse {
  data: {
    name: string;
    owner: string;
    creation: string;
    modified: string;
    modified_by: string;
    docstatus: number;
    workflow_state: string;
    title: string;
    material_request_type: string;
    company: string;
    transaction_date: string;
    schedule_date: string;
    set_warehouse: string;
    status: string;
    per_ordered: number;
    per_received: number;
    items: MaterialRequestResponseItem[];
  };
}
