<lib-page-header [header]="header" [showArrowIcon]="true" [isSearchable]="true" (search)="requestMaterialStore.filterEntities({ filter: { search: $event } })"/>

<div class="flex flex-col h-[calc(100vh-80px)] overflow-y-auto bg-gray-50">
    <main class="flex-1 overflow-auto p-4 space-y-4">
        @for(item of requestMaterialStore.entities(); track item.id){
          <lib-product-card [document]="item" [type]="'reconc'" [showLocation]="false" (click)="navigateToDetails(item.name)"></lib-product-card>
        }
    </main>
  </div>
  <lib-page-footer/>
