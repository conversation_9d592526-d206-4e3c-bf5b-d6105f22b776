import {inject} from '@angular/core';

import {patchState, signalStore, type, withHooks} from '@ngrx/signals';

import { lastValueFrom } from 'rxjs';

import { setAllEntities, withEntities } from '@ngrx/signals/entities';
import { withCallStatus, withEntitiesLocalFilter } from '@ngrx-traits/signals';

import { getTimeAgoAndLocation, normalizeTime } from '@core';
import { InventoryService, PosProfileService } from '@elevar-clients/api';

const entity = type<any>();
export const StockStore = signalStore(
  {providedIn: 'root'},
  withEntities({ entity }),
  withCallStatus({ initialValue: 'loading' }),
  withEntitiesLocalFilter({
    entity,
    defaultFilter: {
      search: '',
    },
    filterFn: (entity, filter) =>
      !filter?.search || 
      Object.values(entity).some(value =>
      typeof value === 'string' && value.toLowerCase().includes(filter.search.toLowerCase())
    ),
  }),

  withHooks(({ setLoaded, setError, ...store }) => ({
    onInit: async () => {
      const stockService = inject(InventoryService);
      const _posProfileService = inject(PosProfileService);

      try {
        const company = _posProfileService.posProfile()?.company
        if(!company) {
            return
        }
        const stocks = (await lastValueFrom(stockService.getStockReconciliations(company))).data;
        const stockWithDetails = stocks.map(item => {
          const { timeAgo, location } = getTimeAgoAndLocation(item, company);
          const normalizedTime = normalizeTime(item.posting_time);
          
          return {
            id: item.name,
            itemCount: 4,
            timeAgo: timeAgo,
            location: location,
            date: item.posting_date,
            time: item.posting_time,
            status: item.workflow_state,
            fullDate: new Date(`${item.posting_date}T${normalizedTime}`)
          };
        });
        patchState(store, setAllEntities(stockWithDetails));
        setLoaded();
      } catch (e) {
        setError(e);
      }
    },
  })),
)


