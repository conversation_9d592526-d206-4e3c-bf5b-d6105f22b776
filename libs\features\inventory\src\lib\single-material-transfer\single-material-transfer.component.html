<div *ngIf="requestMaterialStore.loadMaterialTransferResult() as productDetail"
  class="flex flex-col h-[calc(100vh-80px)]  bg-gray-50">
  <lib-page-header [header]="productDetail.name" [showArrowIcon]="true" [isSearchable]="false" />

  <main class="flex-grow p-4 overflow-y-auto space-y-4">
    <div class="rounded-md overflow-hidden border border-gray-200 shadow-sm">
      <button (click)="toggleAccordion('header')" class="flex justify-between items-center w-full p-4"
        [ngClass]="accordionState.header ? 'bg-primary-hoverGreen text-white' : 'bg-primary-greenLight text-white'"
        type="button">
        <h2 class="text-base font-medium">Material Transfer Details</h2>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 transition-transform"
          [ngClass]="accordionState.header ? 'rotate-180' : ''" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      @if(accordionState.header) {
      <div class="bg-white p-4">
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label for="company" class="block text-sm font-light text-gray-700 mb-1">Company</label>
            <p id="company" class="text-sm font-medium text-gray-900">{{ productDetail.company }}</p>
          </div>
          <div>
            <label for="purpose" class="block text-sm font-light text-gray-700 mb-1">Purpose</label>
            <p class="text-sm font-medium text-gray-900">{{ productDetail.purpose }}</p>
          </div>
          <div>
            <label for="workflow_state" class="block text-sm font-light text-gray-700 mb-1">Workflow State</label>
            <p id="workflow_state" class="text-sm font-medium text-gray-900">{{ productDetail.workflow_state }}</p>
          </div>

        </div>
      </div>
      }
    </div>
    <div class="rounded-md overflow-hidden border border-gray-200 shadow-sm">
      <button (click)="toggleAccordion('items')" class="flex justify-between items-center w-full p-4"
        [ngClass]="accordionState.items ? 'bg-primary-hoverGreen text-white' : 'bg-primary-greenLight text-white'"
        type="button">
        <div class="flex items-center">
          <h2 class="text-base font-medium">Items</h2>
          @if(productDetail.items && productDetail.items.length > 0) {
          <span class="ml-2 text-xs bg-white text-primary-hoverGreen rounded-full px-2 py-0.5">
            {{ productDetail.items.length }}
          </span>
          }
        </div>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 transition-transform"
          [ngClass]="accordionState.items ? 'rotate-180' : ''" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      @if(accordionState.items) {
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead>
            <tr class="bg-primary-greenLight">
              <th class="whitespace-nowrap px-4 py-3.5 text-center text-sm font-medium text-white w-1/2">
                Item Name
              </th>
              <th class="whitespace-nowrap px-4 py-3.5 text-center text-sm font-medium text-white w-1/2">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200 bg-white">
            @if (!productDetail.items || productDetail.items.length === 0) {
            <tr>
              <td colspan="2" class="px-4 py-8 text-center text-sm text-gray-500">
                <div class="flex flex-col items-center justify-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                  <span>No items in material transfer</span>
                </div>
              </td>
            </tr>
            }
            @for(item of productDetail.items; track item.item_code) {
            <tr class="hover:bg-gray-50 transition-colors">
              <td class="whitespace-nowrap px-2 py-4 text-sm">
                <div class="flex flex-col">
                  <span class="font-medium text-gray-900">{{ item.item_code | titlecase }}</span>
                </div>
              </td>
              <td class="whitespace-nowrap px-2 py-4 text-sm text-center">
                <button type="button" class="text-blue-600 underline hover:text-blue-800 cursor-pointer" (click)="openItemModal(item)">
                  View Details
                </button>
              </td>
            </tr>
            }
          </tbody>
        </table>
      </div>
      }
    </div>
    @if (productDetail?.workflow_state === 'Pending Receipt') {
      <div class="flex w-full justify-between gap-4 p-4">
        <lib-button
          aria-label="Reject Material Transfer"
          [buttonType]="isLoading ? 'loading' : 'outline-red'"
          [disabled]="isLoading"
          (click)="handleWorkflowAction('Rejected')"
          class="min-w-[120px]"
        >
          <span>Reject</span>
        </lib-button>
        <lib-button
          aria-label="Mark as Received"
          [buttonType]="isLoading ? 'loading' : 'default'"
          [disabled]="isLoading"
          (click)="handleWorkflowAction('Received')"
          class="min-w-[120px]"
        >
          <span>Receive</span>
        </lib-button>
      </div>
    }
  </main>
</div>

<!-- Modal for item details -->

@if (selectedItem) {
  <div class="fixed inset-0 z-50 flex items-center justify-center p-4">
    <button type="button" tabindex="0" aria-label="Close modal background"
      class="fixed inset-0 bg-black/50 backdrop-blur-sm"
      (click)="closeItemModal()"></button>
    <div class="bg-white rounded-lg shadow-xl w-full max-w-md z-10 overflow-y-auto max-h-[90vh]">
      <div class="flex justify-between items-center p-4 border-b">
        <h2 class="text-lg font-semibold">Item Details</h2>
        <button class="p-2 hover:bg-gray-100 rounded-full" (click)="closeItemModal()">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </button>
      </div>
      <div class="p-4 space-y-4">
        <div class="grid grid-cols-2 gap-4">
          <div>
            <span class="block text-xs text-gray-500">Item Code</span>
            <span class="font-medium text-gray-900">{{ selectedItem.item_code }}</span>
          </div>
          <div>
            <span class="block text-xs text-gray-500">Quantity</span>
            <span class="font-medium text-gray-900">{{ selectedItem.qty }}</span>
          </div>
          <div>
            <span class="block text-xs text-gray-500">Source Warehouse</span>
            <span class="font-medium text-gray-900">{{ selectedItem.s_warehouse }}</span>
          </div>
          <div>
            <span class="block text-xs text-gray-500">Target Warehouse</span>
            <span class="font-medium text-gray-900">{{ selectedItem.t_warehouse }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
}
