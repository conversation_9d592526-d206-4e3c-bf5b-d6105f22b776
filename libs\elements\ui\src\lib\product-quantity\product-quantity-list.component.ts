import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CartItem } from '@models';

@Component({
  selector: 'lib-product-quantity-list',
  imports: [CommonModule],
  templateUrl: './product-quantity-list.component.html',
  styleUrl: './product-quantity-list.component.scss'
})
export class ProductQuantityListComponent {
  @Input() product!: CartItem
  @Output() increment = new EventEmitter<void>();
  @Output() decrement = new EventEmitter<void>();
  @Output() remove = new EventEmitter<void>();

  onIncrement() {
    this.increment.emit();
  }

  onDecrement() {
    this.decrement.emit();
  }

  onRemove() {
    this.remove.emit();
  }
}
