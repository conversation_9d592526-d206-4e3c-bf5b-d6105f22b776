{"name": "pos-client", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "app", "sourceRoot": "apps/pos-client/src", "tags": [], "targets": {"build": {"executor": "@angular-devkit/build-angular:application", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/pos-client", "index": "apps/pos-client/src/index.html", "browser": "apps/pos-client/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/pos-client/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "apps/pos-client/public"}], "styles": ["apps/pos-client/src/styles.scss"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "4kb", "maximumError": "8kb"}], "outputHashing": "all", "serviceWorker": "apps/pos-client/ngsw-config.json"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"executor": "@angular-devkit/build-angular:dev-server", "options": {}, "configurations": {"production": {"buildTarget": "pos-client:build:production"}, "development": {"buildTarget": "pos-client:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"executor": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "pos-client:build"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/pos-client/jest.config.ts"}}, "serve-static": {"executor": "@nx/web:file-server", "options": {"buildTarget": "pos-client:build", "port": 4200, "staticFilePath": "dist/apps/pos-client/browser", "spa": true}}}}