import { Route } from '@angular/router';

export const routes: Route[] = [
  {
    path: 'sales',
    loadComponent: () => import('./invoices/invoices.component').then(c => c.InvoicesComponent),
    title: 'Elevar - Invoices'
  },
  {

    path: 'sales/:id',
    loadComponent: () => import('./single-invoice/single-invoice.component').then(c => c.SingleInvoiceComponent),
    title: 'Elevar - Invoices'
  },
  {
    path: 'create',
    loadComponent: () => import('./new-invoices/new-invoices.component').then(c => c.NewInvoicesComponent),
    title: 'Elevar - Invoices'
  },
  {
    path: 'add-payment',
    loadComponent: () => import('./payment/add-payment/add-payment.component').then(c => c.AddPaymentComponent),
    title: 'Elevar - Invoices'
  },
  {
    path:'new-opening-entry',
    loadComponent: () => import('./new-opening-entry/new-opening-entry.component').then(c => c.New<PERSON>peningEntryComponent),
    title: 'Elevar - Invoices'
  },
  {
    path:'new-closing-entry',
    loadComponent: () => import('./new-closing-entry/new-closing-entry.component').then(c => c.NewClosingEntryComponent),
    title: 'Elevar - Invoices'
  },
  {
    path: '',
    redirectTo: '/invoices/sales',
    pathMatch: 'full'
  }
]
