import {inject} from '@angular/core';
import {patchState, signalStore, type, withHooks} from '@ngrx/signals';

import { lastValueFrom } from 'rxjs';

import { setAllEntities, withEntities } from '@ngrx/signals/entities';
import { withCalls, withCallStatus, withEntitiesLocalFilter, withEntitiesSingleSelection } from '@ngrx-traits/signals';
import { MaterialTransferService } from '@elevar-clients/api';


const entity = type<any>();

export const MaterialTransferStore = signalStore(
  {providedIn: 'root'},
  withEntities({ entity }),
  withCallStatus({ initialValue: 'loading' }),
  withEntitiesSingleSelection({ entity }),

  withEntitiesLocalFilter({
    entity,
    defaultFilter: {
      search: '',
    },
    filterFn: (entity, filter) =>
      !filter?.search || 
      Object.values(entity).some(value =>
      typeof value === 'string' && value.toLowerCase().includes(filter.search.toLowerCase())
    ),
  }),


  withHooks(({ setLoaded, setError, ...store }) => ({
    onInit: async () => {
      const  materialService = inject(MaterialTransferService);
      try {
        const requests = (await lastValueFrom(materialService.getMaterialTransfersByCompany()));
        const requestsWithIds = requests.map((request, index) => ({
          ...request,
          id: index + 1,
        }));
        patchState(store, setAllEntities(requestsWithIds));
        setLoaded();
      } catch (e) {
        setError(e);
      }
    },
  })),

  withCalls(() => ({
    // loadProductDetailResult()
    loadMaterialTransfer: ({ id }: { id: string }) =>
      inject(MaterialTransferService).getMaterialRequestById(id),
   })),
)

