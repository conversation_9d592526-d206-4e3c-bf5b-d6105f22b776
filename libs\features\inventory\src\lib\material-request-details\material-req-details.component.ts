import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from '@elements/ui';
import { ActivatedRoute } from '@angular/router';
import { LoaderComponent } from '@elements/ui';

import { tap } from 'rxjs';
import { NotificationService } from '@core';
import { MaterialRequestService } from '@elevar-clients/api';

@Component({
  selector: 'lib-material-req-details',
  imports: [CommonModule, SharedModule, LoaderComponent],
  templateUrl: './material-req-details.component.html',
  styleUrl: './material-req-details.component.scss'
})
export class MaterialReqDetailsComponent {
  private readonly _route = inject(ActivatedRoute);
  private readonly _materialService = inject(MaterialRequestService);
  private readonly _notificationService = inject(NotificationService);

  showLoader = false;

  stockName = this._route.snapshot.params['id'];
  stock$ = this.loadStock();

  loadStock() {
    return this._materialService.getMaterialRequestById(this.stockName)
  }

  // Accordion states
  accordionState = {
    header: true,
    items: true,
    payments: true,
    summary: true
  };

  loading = false;

  /**
   * Toggle the specified accordion section
   * @param section The accordion section to toggle
   */
  toggleAccordion(section: 'header' | 'items' | 'payments' | 'summary'): void {
    this.accordionState[section] = !this.accordionState[section];
  }

  /**
   * Calculate the total amount from invoice items
   */
  calculateTotal(items: any[]): number {
    return items.reduce((total, item) => total + item.amount, 0);
  }

  /**
   * Calculate the total amount from invoice payments
   */
  calculateTotalPayments(payments:any[]): number {
    return payments.reduce((total, payment) => total + payment.amount, 0);
  }

  submitMaterialRequest(stock: any) {
    this.loading = true;
    this.showLoader = true;
    this._materialService.update(stock.name, { workflow_state: 'Pending Approval' }).subscribe({
      next: () => {
        this._notificationService.setSuccess('Submitted', 'Material request submitted successfully.');
        this.loading = false;
        this.stock$ = this.loadStock();
        setTimeout(() => { this.showLoader = false; }, 800); // brief loader for UX
      },
      error: () => {
        this._notificationService.setError('Error', 'Failed to submit material request.');
        this.loading = false;
        this.showLoader = false;
      }
    });
  }
}
