import { Component, computed, inject } from '@angular/core';
import { RouterModule } from '@angular/router';
import { SplashScreenComponent } from "./splash-screen/splash-screen.component";
import { ElevarModalComponent } from "@elements/ui";
import { NotificationService } from '@core';
import { AuthService } from '@elevar-clients/api';

@Component({
  imports: [RouterModule, SplashScreenComponent, ElevarModalComponent],
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
})
export class AppComponent {
  initialLoadIsDone = false;
  private readonly _authService = inject(AuthService);
  private readonly _notificationService = inject(NotificationService);
  
  isLoggedIn = this._authService.isLoggedIn()
  isOpen = computed(() => this._notificationService.NotificationState().isOpen);
  title = computed(() => this._notificationService.NotificationState().title);
  description = computed(() => this._notificationService.NotificationState().description);
  type = computed(() => this._notificationService.NotificationState().type);

  constructor() {
    setTimeout(() => {
      this.initialLoadIsDone = true;
    }, 3000);
  }
}
