import { Component, inject, OnDestroy, Signal, signal, WritableSignal } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { finalize, Observable, tap } from 'rxjs';

import { POSInvoice, PosOpeningEntry } from '@models';
import { InvoiceStore } from '@state';

import { ButtonComponent, PageFooterComponent, PageHeaderComponent, ProductCardComponent } from '@elements/ui';
import { NotificationService } from '@core';
import { InvoiceService } from '@elevar-clients/api';
@Component({
  selector: 'lib-invoices',
  imports: [CommonModule, ProductCardComponent, PageHeaderComponent, PageFooterComponent, ButtonComponent],
  templateUrl: './invoices.component.html',
  styleUrls: ['./invoices.component.scss']
})
export class InvoicesComponent implements OnDestroy {
  private readonly _router = inject(Router);
  private readonly _notificationService = inject(NotificationService);
  private readonly _invoiceService = inject(InvoiceService);
  invoiceStore = inject(InvoiceStore);

  number = signal(0);

  searchQuery = signal<string>('');
  header = 'Sale Invoices';

  invoices$: Observable<POSInvoice[]> = this._invoiceService.getAllInvoices().pipe(
    finalize(() => this.isLoading.set(false)),
    tap((res) => this.invoices = res)
  )

  hasOpeningEntryToday: WritableSignal<boolean> = signal<boolean>(false);
  private readonly _mostRecentEntry: WritableSignal<PosOpeningEntry | null> = signal<PosOpeningEntry | null>(null);

  trackOpeningEntry$ = this._invoiceService.checkIfUserCanCreateInvoice().pipe(tap(res => {
    if (!res.isLastEntryClosed && !res.hasOpeningEntryToday) {
      this._mostRecentEntry.set(res.mostRecentEntry as PosOpeningEntry);
      this._router.navigateByUrl('/invoices/new-closing-entry', { state: { mostRecentEntry: res.mostRecentEntry } });
      return
    }
    if (!res.hasOpeningEntryToday) {
      this.navigateToOpenEntries();
    }
    this.hasOpeningEntryToday.set(res.hasOpeningEntryToday);
    this.checkIfUserCanRoute = !!res.isLastEntryClosed;
  }))

  invoices!: POSInvoice[];
  signalInvoice!: Signal<number | undefined>;

  isLoading = signal(true);

  checkIfUserCanRoute = false;

  onSearchUpdated(sq: string) {
    this.searchQuery.set(sq.trim());
  }

  navigateToInvoiceDetail(name: string) {
    this._router.navigateByUrl(`/invoices/sales/${name}`);
  }

  getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'return':
        return 'text-red-500 bg-red-100'
      case 'paid':
        return 'text-green-500 bg-green-100'
      case 'new invoice':
        return 'text-blue-500 bg-blue-100'
      default:
        return 'text-gray-500 bg-gray-100'
    }
  }

  navigateToInvoicePage() {
    this._router.navigate(['/invoices/create']);
  }

  order() {
    this.invoiceStore.loadByOrder({ order: 'asc' });
  }

  navigateToOpenEntries() {
    this._router.navigate(['/invoices/new-opening-entry']);
  }

  navigateToCloseEntries() {
    if (this._mostRecentEntry()) {
      this._router.navigate(['/invoices/new-closing-entry'], { state: { mostRecentEntry: this._mostRecentEntry() } });
    } else {
      this._notificationService.setWarning('No opening entry found', 'No opening entry found. Kindly contact Administrator.');
    }
  }

  onSearch(event: string): void {
    this.invoiceStore.filterEntities({ filter: { search: event }});
  }

  ngOnDestroy(): void {
    this.invoiceStore.resetEntitiesFilter();
  }

}