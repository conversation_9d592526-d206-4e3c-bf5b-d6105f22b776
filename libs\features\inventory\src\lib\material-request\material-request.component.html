<lib-page-header [header]="header" [showArrowIcon]="true" [isSearchable]="true" [placeholder]="'Search Material Request'" (search)="store.filterEntities({ filter: { search: $event } })"/>
<div class="flex flex-col h-[calc(100vh-80px)] bg-gray-50">
  <main class="flex-1 overflow-auto p-4 space-y-4 max-h-[calc(100vh-255px)]">
    @for (req of store.entities(); track req.name) {
      <lib-product-card [document]="req" [type]="'request'" [showLocation]="false" (click)="navigatetoMaterialRequestDetails(req.name)"/>
    }
  </main>
  <lib-button type="submit" class="fixed right-4 bottom-20 rounded-full" (click)="navigateToNewRequest()">
    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2">
      <line x1="12" y1="5" x2="12" y2="19"></line>
      <line x1="5" y1="12" x2="19" y2="12"></line>
    </svg>
    <span>Request Material</span>
  </lib-button>
  <lib-page-footer />
</div>
