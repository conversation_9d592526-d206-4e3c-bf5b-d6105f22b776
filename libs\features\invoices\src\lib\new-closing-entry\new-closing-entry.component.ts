import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormControl, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { ButtonComponent, PageHeaderComponent, PageFooterComponent, ElevarInputsComponent, LoaderComponent } from "@elements/ui";
import { PaymentReconciliation, PosOpeningEntry, POSOpeningEntryDetailResponse, POSTransaction } from '@models';
import { catchError, EMPTY, map, Observable, tap } from 'rxjs';
import { Router } from '@angular/router';
import { NotificationService } from '@core';
import { PosService } from '@elevar-clients/api';

@Component({
  selector: 'lib-new-closing-entry',
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ButtonComponent,
    PageFooterComponent,
    ElevarInputsComponent,
    PageHeaderComponent,
    LoaderComponent
  ],
  templateUrl: './new-closing-entry.component.html',
  styleUrls: ['./new-closing-entry.component.scss']
})
export class NewClosingEntryComponent {
  header = 'POS Closing Entry';
  private readonly _fb = inject(FormBuilder);
  private readonly _posService = inject(PosService);
  private readonly _router = inject(Router);
  private readonly _notificationService = inject(NotificationService);
  isLoading = false;

  constructor() {
    this._setMostRecentHistory();
  }

  formGroup = this._fb.group({
    cash_amount: ['', [Validators.required, Validators.min(0)]],
    mpesa_amount: ['', [Validators.required, Validators.min(0)]]
  });

  submit$: Observable<PosOpeningEntry> = new Observable()
  mostRecentEntry: POSOpeningEntryDetailResponse | null = null;
  mostRecentEntry$: Observable<POSOpeningEntryDetailResponse> = new Observable();

  get formIsValid(): boolean {
    return this.formGroup.valid;
  }

  onSubmit(event: Event): void {
    event.preventDefault();
    if (!this.formIsValid) return;

    if (!this.mostRecentEntry) {
      this._notificationService.setError('POS Closing Entry', 'No recent entry found.');
      return;
    }

    this.isLoading = true;

    const paymentReconciliations: PaymentReconciliation[] = [
      {
        mode_of_payment: 'Cash',
        closing_amount: parseFloat(this.formGroup.get('cash_amount')?.value as string),
        opening_amount: this.mostRecentEntry.balance_details.find(b => b.mode_of_payment === 'Cash')?.opening_amount ?? 0,
        expected_amount: 0
      },
      {
        mode_of_payment: 'M-Pesa',
        closing_amount: parseFloat(this.formGroup.get('mpesa_amount')?.value as string),
        opening_amount: this.mostRecentEntry.balance_details.find(b => b.mode_of_payment === 'M-Pesa')?.opening_amount ?? 0,
        expected_amount: 0
      }
    ];

    const posTransactions: POSTransaction[] = [];

    this.submit$ = this._posService.createClosingEntry(this.mostRecentEntry.name, paymentReconciliations, posTransactions).pipe(
      tap((res) => {
        this.isLoading = false;
        if (res) {
          this._notificationService.setSuccess('POS Opening entry', 'Creation of POS Opening entry was successful.');
          setTimeout(() => {
            this._notificationService.clearNotification();
          }, 3000);
          this.formGroup.reset();

          this.navigateToInvoicePage();
        } else {
          this._notificationService.setError('POS Opening entry', 'Creation of POS Opening entry failed.');
          setTimeout(() => {
            this._notificationService.clearNotification();
          }, 3000);
        }
      }),
      catchError(err => {
        console.log(err);
        this.isLoading = false;
        this._notificationService.setError('POS Opening entry', `Creation of POS Opening entry failed because of ${JSON.stringify(err)}`);
        return EMPTY;
      })
    );
  }

  get cashControl(): FormControl {
    return this.formGroup.get('cash_amount') as FormControl;
  }

  get mpesaControl(): FormControl {
    return this.formGroup.get('mpesa_amount') as FormControl;
  }

  private _setMostRecentHistory() {
    const mostRecentEntryFromSessionStorage = JSON.parse(sessionStorage.getItem('most-recent-opening-entry') ?? 'null');

    if (mostRecentEntryFromSessionStorage) {
      this.mostRecentEntry = mostRecentEntryFromSessionStorage;
      this.mostRecentEntry$ = this._posService.getOpeningEntry(mostRecentEntryFromSessionStorage.name).pipe(map(res => {
        this.mostRecentEntry = res.data;
        return res.data;
      }));
      return;
    }
    const state = this._router.getCurrentNavigation()?.extras.state;
    if (state) {
      const data = state['mostRecentEntry']
      if (data) {
        this.mostRecentEntry$ = this._posService.getOpeningEntry(data.name).pipe(map(res => {
          this.mostRecentEntry = res.data;
          return res.data;
        }));
        return;
      }
      this._notificationService.setError('Error fetching last open entry', 'There was an error fetching the last opening entry.');
      this.navigateToInvoicePage();
    } else {
      this._notificationService.setError('No state provided', 'Unable to retrieve the last opening entry without state.');
      this.navigateToInvoicePage();
    }
  }

  navigateToInvoicePage() {
    this._router.navigate(['/invoices']);
  }

}
