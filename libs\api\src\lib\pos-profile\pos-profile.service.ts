import { Injectable, signal, WritableSignal } from '@angular/core';
import { HttpHeaders, HttpParams } from '@angular/common/http';
import { BaseHttpService } from '@core';
import { Observable, catchError, map, tap, throwError } from 'rxjs';
import { Employee, FrappeListResponse, POSProfile, Warehouse } from '@models';

@Injectable({
  providedIn: 'root'
})
export class PosProfileService extends BaseHttpService<FrappeListResponse<POSProfile>> {
  private readonly _baseUrl = 'https://elevar-develop.frappe.cloud';
  private readonly _endpoint = 'api/resource/POS Profile';
  private readonly _warehouseEndpoint = 'api/resource/Warehouse';

  posProfile: WritableSignal<POSProfile | null> = signal(JSON.parse(localStorage.getItem('posProfile') ?? 'null'));

  loggedInEmployee!: Employee;

  constructor() {
    super();
    this.initializeService(this._endpoint, this._baseUrl);
    this.posProfile.set(JSON.parse(localStorage.getItem('posProfile') ?? 'null'));
  }

  protected override getHeaders(): HttpHeaders {
    return new HttpHeaders({
      'Content-Type': 'application/x-www-form-urlencoded'
    });
  }

  getPOSProfileForEmployeeInBranch(employee: Employee): Observable<POSProfile> {
    // Validate input
    if (!employee?.company || !employee?.branch) {
      return throwError(() => new Error('Invalid employee data'));
    }

    // Construct parameters
    const params = new HttpParams()
      .set('filters', JSON.stringify([
        ["company", "=", employee.company],
        ["disabled", "=", "0"],
        ["branch", "=", employee.branch]
      ]))
      .set('fields', JSON.stringify([
        "name", "company", "branch",
        "customer", "warehouse", "selling_price_list"
      ]));

    return this.http
      .get<FrappeListResponse<POSProfile>>(
        `${this.apiUrl}`,
        { params, headers: this.getHeaders(), withCredentials: true }
      )
      .pipe(
        map(response => {
          if (!response.data?.length) {
            throw new Error('No matching POS profile found');
          }
          return response.data[0];
        }),
        tap(profile => {
          localStorage.setItem('posProfile', JSON.stringify(profile));
          this.posProfile.set(profile);
        }),
        catchError(error => {
          console.error('POS Profile fetch failed:', error);
          return throwError(() => new Error('Failed to load POS profile'));
        })
      );
  }

  getWarehousesForCompany(companyProvided?:string): Observable<Warehouse[]> {
    // Construct parameters based on curl request
    this.initializeService(this._warehouseEndpoint, this._baseUrl);
    const company = companyProvided ?? this.posProfile()?.company;
    const params = new HttpParams()
      .set('filters', JSON.stringify([
        ["company", "=", company],
        ["is_group", "=", "0"]
      ]))
      .set('fields', JSON.stringify([
        "warehouse_name",
        "warehouse_type",
        "company"
      ]));

    return this.http
      .get<FrappeListResponse<Warehouse>>(
        `${this.apiUrl}`,
        { params, headers: this.getHeaders(), withCredentials: true }
      )
      .pipe(
        map(response => response.data || []),
        tap(() => this.initializeService(this._endpoint, this._baseUrl)),
        catchError(error => {
          console.error('Warehouse fetch failed:', error);
          this.initializeService(this._endpoint, this._baseUrl);
          return throwError(() => new Error('Failed to load warehouses'));
        })
      );
  }
}